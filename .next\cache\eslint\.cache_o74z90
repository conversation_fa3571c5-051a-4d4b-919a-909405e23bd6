[{"C:\\Users\\<USER>\\Desktop\\poooo\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\AutomationAnimation.tsx": "3", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\CaseStudy.tsx": "4", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\ContactModal.tsx": "5", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\FAQ.tsx": "6", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\FloatingContactButton.tsx": "7", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Footer.tsx": "8", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Header.tsx": "9", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Hero.tsx": "10", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Insights.tsx": "11", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Story.tsx": "12", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Testimonials.tsx": "13", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\lib\\analytics.ts": "14", "C:\\Users\\<USER>\\Desktop\\poooo\\src\\lib\\supabase.ts": "15"}, {"size": 1961, "mtime": 1751364417629, "results": "16", "hashOfConfig": "17"}, {"size": 953, "mtime": 1751364417636, "results": "18", "hashOfConfig": "17"}, {"size": 1066, "mtime": 1751364417643, "results": "19", "hashOfConfig": "17"}, {"size": 2231, "mtime": 1751364417652, "results": "20", "hashOfConfig": "17"}, {"size": 4701, "mtime": 1751364865768, "results": "21", "hashOfConfig": "17"}, {"size": 3591, "mtime": 1751364417679, "results": "22", "hashOfConfig": "17"}, {"size": 803, "mtime": 1751364417679, "results": "23", "hashOfConfig": "17"}, {"size": 4110, "mtime": 1751364417695, "results": "24", "hashOfConfig": "17"}, {"size": 1622, "mtime": 1751364417695, "results": "25", "hashOfConfig": "17"}, {"size": 1519, "mtime": 1751364417708, "results": "26", "hashOfConfig": "17"}, {"size": 1746, "mtime": 1751364417712, "results": "27", "hashOfConfig": "17"}, {"size": 1331, "mtime": 1751364895005, "results": "28", "hashOfConfig": "17"}, {"size": 10647, "mtime": 1751364417745, "results": "29", "hashOfConfig": "17"}, {"size": 1442, "mtime": 1751364417750, "results": "30", "hashOfConfig": "17"}, {"size": 3436, "mtime": 1751364417762, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ilhw6e", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\poooo\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\AutomationAnimation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\CaseStudy.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\ContactModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\FAQ.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\FloatingContactButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Insights.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Story.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\components\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\lib\\analytics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\poooo\\src\\lib\\supabase.ts", [], []]