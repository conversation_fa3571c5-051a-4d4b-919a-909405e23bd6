"use client";

export default function FloatingContactButton() {
  const openContactModal = () => {
    const modal = document.getElementById("contactModal");
    if (modal) {
      modal.classList.add("active");
      document.body.style.overflow = "hidden";
    }
  };

  return (
    <button
      className="floating-contact-btn"
      onClick={openContactModal}
      id="floatingContactBtn"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
      </svg>
      Get In Touch
    </button>
  );
}
