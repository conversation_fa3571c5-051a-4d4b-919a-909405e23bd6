/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Brand Colors (from Portfolio-color-palette.md)
        primary: {
          cyan: "#17b8dd",
          "dark-cyan": "#2da8c7",
        },
        success: {
          green: "#51b85f",
        },
        warning: {
          orange: "#f1ad4e",
        },
        // Typography Colors
        text: {
          primary: "#e4e4eb",
          secondary: "#c4c5c9",
          light: "#8b8b9b",
          white: "#ffffff",
        },
        // Background Colors
        bg: {
          primary: "#0a0a0a",
          secondary: "#1a1a23",
          card: "rgba(37, 42, 46, 0.8)",
          transparent: "rgba(66, 184, 221, 0.1)",
          glass: "rgba(66, 184, 221, 0.05)",
        },
      },
      fontFamily: {
        sans: [
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Arial",
          "sans-serif",
        ],
      },
      spacing: {
        xs: "8px",
        sm: "16px",
        md: "24px",
        lg: "40px",
        xl: "64px",
        "2xl": "96px",
      },
      boxShadow: {
        "glow-cyan": "0 0 20px rgba(23, 184, 221, 0.5)",
        "glow-subtle": "0 0 10px rgba(66, 184, 221, 0.3)",
      },
      animation: {
        "pulse-slow": "pulse 3s ease-in-out infinite",
        float: "float 6s ease-in-out infinite",
        glow: "glow 2s ease-in-out infinite alternate",
      },
      keyframes: {
        float: {
          "0%, 100%": { transform: "translateY(0px)" },
          "50%": { transform: "translateY(-10px)" },
        },
        glow: {
          "0%": { boxShadow: "0 0 20px rgba(23, 184, 221, 0.5)" },
          "100%": { boxShadow: "0 0 30px rgba(23, 184, 221, 0.8)" },
        },
      },
    },
  },
  plugins: [],
};
