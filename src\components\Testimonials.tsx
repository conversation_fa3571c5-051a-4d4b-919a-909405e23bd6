'use client'

export default function Testimonials() {
  return (
    <section id="testimonials" className="section">
      <div className="container">
        <div className="testimonials-header">
          <h2>What People Are Saying</h2>
          <p>Real results from real clients</p>
        </div>
      </div>

      <div className="testimonial-mosaic">
        <div className="fade-edge-left"></div>
        <div className="fade-edge-right"></div>

        <div className="mosaic-container">
          {/* Column 1 */}
          <div className="mosaic-column">
            <div className="testimonial-card video">
              <div className="video-play"></div>
              <div style={{ position: 'absolute', bottom: '20px', left: '20px' }}>
                <div className="testimonial-author">John D.</div>
                <div className="testimonial-role">CEO & Founder</div>
              </div>
            </div>

            <div className="testimonial-card large gold-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-text testimonial-quote">
                "Since implementing <PERSON>'s automations, our efficiency has skyrocketed. He doesn't just do automation, he transforms businesses."
              </div>
              <div style={{ marginTop: '20px' }}>
                <div className="testimonial-author">Maria Rodriguez</div>
                <div className="testimonial-role">CEO Digital Agency</div>
              </div>
            </div>

            <div className="testimonial-card small">
              <div className="testimonial-stat">300%</div>
              <div className="testimonial-metric">process efficiency in 3 months!</div>
              <div style={{ marginTop: '12px' }}>
                <div className="testimonial-author">James Wilson</div>
              </div>
            </div>
          </div>

          {/* Column 2 */}
          <div className="mosaic-column">
            <div className="testimonial-card medium green-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-header">
                <div className="testimonial-avatar">AT</div>
                <div className="testimonial-info">
                  <div className="testimonial-author">Alex Turner</div>
                  <div className="testimonial-role">Agency Owner</div>
                </div>
              </div>
              <div className="testimonial-text">
                "Best investment in our operations ever. The AI workflows alone are worth 10x what we paid."
              </div>
            </div>

            <div className="testimonial-card large">
              <div className="testimonial-text">
                "Just implemented Denis's AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof 🚀"
              </div>
              <div style={{ marginTop: '20px' }}>
                <div className="testimonial-author">Emma Davis</div>
                <div className="testimonial-role">Marketing Director</div>
              </div>
            </div>

            <div className="testimonial-card video">
              <div className="video-play"></div>
              <div style={{ position: 'absolute', bottom: '20px', left: '20px' }}>
                <div className="testimonial-author">Lisa Martinez</div>
                <div className="testimonial-role">Startup Founder</div>
              </div>
            </div>
          </div>

          {/* Column 3 */}
          <div className="mosaic-column">
            <div className="testimonial-card small gold-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-text">
                "1700% growth sounds impossible until you work with Denis. He delivered exactly that for our startup's sales pipeline!"
              </div>
              <div style={{ marginTop: '12px' }}>
                <div className="testimonial-author">Michael Brown</div>
              </div>
            </div>

            <div className="testimonial-card large">
              <div className="testimonial-header">
                <div className="testimonial-avatar">JL</div>
                <div className="testimonial-info">
                  <div className="testimonial-author">Jennifer Lee</div>
                  <div className="testimonial-role">Local Business</div>
                </div>
              </div>
              <div className="testimonial-text">
                "Automated our lead qualification. 300% faster response times. Our sales team is closing deals quicker than ever."
              </div>
            </div>

            <div className="testimonial-card medium green-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-stat">10x</div>
              <div className="testimonial-metric">Operational Efficiency. Best investment in our business ever!</div>
              <div style={{ marginTop: '16px' }}>
                <div className="testimonial-author">Sarah Chen</div>
                <div className="testimonial-role">Business Owner</div>
              </div>
            </div>
          </div>

          {/* Column 4 */}
          <div className="mosaic-column">
            <div className="testimonial-card medium">
              <div className="testimonial-text testimonial-quote">
                "an unmatched skill in automation"
              </div>
              <div style={{ marginTop: '16px' }}>
                <div className="testimonial-author">Patricia Williams</div>
                <div className="testimonial-role">Creative Director</div>
              </div>
            </div>

            <div className="testimonial-card small gold-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-header">
                <div className="testimonial-avatar">KT</div>
                <div className="testimonial-info">
                  <div className="testimonial-author">Kevin Taylor</div>
                  <div className="testimonial-role">B2B SaaS Platform</div>
                </div>
              </div>
              <div className="testimonial-text">
                "Automated our data entry and reporting, saving countless hours."
              </div>
            </div>

            <div className="testimonial-card large green-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-text">
                "Just implemented Denis's AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof."
              </div>
              <div style={{ marginTop: '20px' }}>
                <div className="testimonial-author">Robert Anderson</div>
                <div className="testimonial-role">VP of Sales</div>
              </div>
            </div>
          </div>

          {/* Duplicate columns for continuous scroll */}
          <div className="mosaic-column">
            <div className="testimonial-card video">
              <div className="video-play"></div>
              <div style={{ position: 'absolute', bottom: '20px', left: '20px' }}>
                <div className="testimonial-author">John D.</div>
                <div className="testimonial-role">CEO & Founder</div>
              </div>
            </div>

            <div className="testimonial-card large gold-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-text testimonial-quote">
                "Since implementing Denis's automations, our efficiency has skyrocketed. He doesn't just do automation, he transforms businesses."
              </div>
              <div style={{ marginTop: '20px' }}>
                <div className="testimonial-author">Maria Rodriguez</div>
                <div className="testimonial-role">CEO Digital Agency</div>
              </div>
            </div>

            <div className="testimonial-card small">
              <div className="testimonial-stat">300%</div>
              <div className="testimonial-metric">process efficiency in 3 months!</div>
              <div style={{ marginTop: '12px' }}>
                <div className="testimonial-author">James Wilson</div>
              </div>
            </div>
          </div>

          <div className="mosaic-column">
            <div className="testimonial-card medium green-accent">
              <div className="testimonial-accent"></div>
              <div className="testimonial-header">
                <div className="testimonial-avatar">AT</div>
                <div className="testimonial-info">
                  <div className="testimonial-author">Alex Turner</div>
                  <div className="testimonial-role">Agency Owner</div>
                </div>
              </div>
              <div className="testimonial-text">
                "Best investment in our operations ever. The AI workflows alone are worth 10x what we paid."
              </div>
            </div>

            <div className="testimonial-card large">
              <div className="testimonial-text">
                "Just implemented Denis's AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof 🚀"
              </div>
              <div style={{ marginTop: '20px' }}>
                <div className="testimonial-author">Emma Davis</div>
                <div className="testimonial-role">Marketing Director</div>
              </div>
            </div>

            <div className="testimonial-card video">
              <div className="video-play"></div>
              <div style={{ position: 'absolute', bottom: '20px', left: '20px' }}>
                <div className="testimonial-author">Lisa Martinez</div>
                <div className="testimonial-role">Startup Founder</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
