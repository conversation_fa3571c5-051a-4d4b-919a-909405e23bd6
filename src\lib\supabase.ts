import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export interface Contact {
  id: string
  name: string
  email: string
  message: string
  source: string
  created_at: string
}

export interface Analytics {
  id: string
  page: string
  user_agent?: string
  referrer?: string
  ip_address?: string
  created_at: string
}

// Database Operations
export const db = {
  // Contact operations
  contacts: {
    async create(contact: Omit<Contact, 'id' | 'created_at'>) {
      const { data, error } = await supabase
        .from('contacts')
        .insert([contact])
        .select()
        .single()
      
      if (error) throw error
      return data
    },

    async getAll() {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (error) throw error
      return data
    },

    async getById(id: string) {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .eq('id', id)
        .single()
      
      if (error) throw error
      return data
    }
  },

  // Analytics operations
  analytics: {
    async track(analytics: Omit<Analytics, 'id' | 'created_at'>) {
      const { data, error } = await supabase
        .from('analytics')
        .insert([analytics])
        .select()
        .single()
      
      if (error) throw error
      return data
    },

    async getPageViews(page?: string) {
      let query = supabase
        .from('analytics')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (page) {
        query = query.eq('page', page)
      }
      
      const { data, error } = await query
      
      if (error) throw error
      return data
    },

    async getStats() {
      const { data, error } = await supabase
        .from('analytics')
        .select('page, created_at')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      
      if (error) throw error
      
      // Process stats
      const pageViews = data.length
      const uniquePages = new Set(data.map(item => item.page)).size
      const dailyViews = data.reduce((acc, item) => {
        const date = new Date(item.created_at).toDateString()
        acc[date] = (acc[date] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      return {
        totalViews: pageViews,
        uniquePages,
        dailyViews
      }
    }
  }
}

// Real-time subscriptions
export const subscriptions = {
  contacts: (callback: (payload: any) => void) => {
    return supabase
      .channel('contacts')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'contacts' }, 
        callback
      )
      .subscribe()
  },

  analytics: (callback: (payload: any) => void) => {
    return supabase
      .channel('analytics')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'analytics' }, 
        callback
      )
      .subscribe()
  }
}
