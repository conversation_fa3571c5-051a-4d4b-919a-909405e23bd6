import { db } from './supabase'

export interface AnalyticsData {
  page: string
  user_agent?: string
  referrer?: string
  ip_address?: string
}

export async function trackPageView(page: string) {
  try {
    const analyticsData: AnalyticsData = {
      page,
      user_agent:
        typeof window !== 'undefined' ? navigator.userAgent : undefined,
      referrer: typeof window !== 'undefined' ? document.referrer : undefined,
    }

    // Only track in production or when explicitly enabled
    if (
      process.env.NODE_ENV === 'production' ||
      process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'
    ) {
      await db.analytics.track(analyticsData)
    }
  } catch (error) {
    // Silently fail analytics tracking to not affect user experience
    console.warn('Analytics tracking failed:', error)
  }
}

export async function trackEvent(
  eventName: string,
  properties?: Record<string, any>
) {
  try {
    const analyticsData: AnalyticsData = {
      page: `event:${eventName}`,
      user_agent:
        typeof window !== 'undefined' ? navigator.userAgent : undefined,
      referrer: typeof window !== 'undefined' ? document.referrer : undefined,
    }

    if (
      process.env.NODE_ENV === 'production' ||
      process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true'
    ) {
      await db.analytics.track(analyticsData)
    }
  } catch (error) {
    console.warn('Event tracking failed:', error)
  }
}
