<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="DENIS - AI & Business Automation Expert. Transform your business with strategic AI implementation and automation solutions.">
    <title>DENIS | AI & Business Automation Expert</title>

    <style>
        /* ============================================
           CSS RESET & ROOT VARIABLES
           ============================================ */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Brand Colors */
            --primary-cyan: #17b8dd;
            --dark-cyan: #2da8c7;
            --success-green: #51b85f;
            --warning-orange: #f1ad4e;

            /* Dark Theme Colors */
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a23;
            --bg-card: rgba(37, 42, 46, 0.8);
            --bg-transparent: rgba(66, 184, 221, 0.1);
            --bg-glass: rgba(66, 184, 221, 0.05);

            /* Text Colors */
            --text-primary: #e4e4eb;
            --text-secondary: #c4c5c9;
            --text-light: #8b8b9b;
            --text-white: #ffffff; /* Added white for emphasis */

            /* Effects */
            --glow-cyan: 0 0 20px rgba(23, 184, 221, 0.5);
            --glow-subtle: 0 0 10px rgba(66, 184, 221, 0.3);

            /* Spacing */
            --space-xs: 8px;
            --space-sm: 16px;
            --space-md: 24px;
            --space-lg: 40px;
            --space-xl: 64px;
            --space-2xl: 96px;

            /* Typography */
            --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-sans);
            color: var(--text-secondary);
            background: var(--bg-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Typography */
        h1 {
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 800;
            line-height: 1.1;
            letter-spacing: -0.02em;
            color: var(--text-white); /* Default to white for H1 */
        }

        h1 .highlight {
            background: linear-gradient(135deg, #23b6dd 0%, #23a6c7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h2 {
            font-size: clamp(2rem, 4vw, 2.75rem);
            font-weight: 700;
            line-height: 1.2;
            color: var(--text-primary);
            letter-spacing: -0.01em;
        }

        h3 {
            font-size: clamp(1.25rem, 3vw, 1.5rem);
            font-weight: 600;
            line-height: 1.3;
            color: var(--primary-cyan);
        }

        p {
            font-size: 1.125rem;
            line-height: 1.7;
            color: var(--text-secondary);
        }

        /* Utilities */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-md);
        }

        .section {
            padding: var(--space-xl) 0;
            position: relative;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 14px 32px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #23b6dd 0%, #23a6c7 100%);
            color: #ffffff;
            box-shadow: 0 4px 15px rgba(23, 184, 221, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(23, 184, 221, 0.5);
        }

        .btn-secondary {
            background: transparent;
            color: var(--primary-cyan);
            border: 2px solid var(--primary-cyan);
        }

        .btn-secondary:hover {
            background: var(--bg-transparent);
            box-shadow: var(--glow-subtle);
        }

        /* ============================================
           HEADER & NAVIGATION
           ============================================ */
        header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(26, 26, 35, 0.95);
            backdrop-filter: blur(10px);
            z-index: 1000;
            border-bottom: 1px solid rgba(66, 184, 221, 0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 80px;
            padding: 0 var(--space-lg);
        }

        .logo {
            font-size: 1.75rem;
            font-weight: 800;
            background: linear-gradient(135deg, #17b8dd 0%, #2da8c7 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: var(--space-lg);
            list-style: none;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--primary-cyan);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-cyan);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* Mobile Menu */
        .mobile-menu {
            display: none;
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
        }

        .mobile-menu span {
            display: block;
            width: 24px;
            height: 2px;
            background: var(--primary-cyan);
            margin: 5px 0;
            transition: all 0.3s ease;
        }

        /* Floating Get In Touch Button */
        .floating-contact-btn {
            position: fixed;
            bottom: var(--space-lg);
            right: var(--space-lg);
            background: linear-gradient(135deg, #23b6dd 0%, #23a6c7 100%);
            color: #ffffff;
            padding: 12px 24px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            box-shadow: 0 8px 20px rgba(23, 184, 221, 0.4);
            z-index: 1001;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .floating-contact-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(23, 184, 221, 0.6);
        }

        /* Modal Styles */
        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 2000; /* Sit on top */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgba(0,0,0,0.8); /* Black w/ opacity */
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modal.active {
            display: flex;
            opacity: 1;
        }

        .modal-content {
            background: var(--bg-secondary);
            margin: auto;
            padding: var(--space-xl);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.5);
            max-width: 600px;
            width: 90%;
            position: relative;
            transform: translateY(20px);
            transition: transform 0.3s ease;
            border: 1px solid rgba(66, 184, 221, 0.1);
        }

        .modal.active .modal-content {
            transform: translateY(0);
        }

        .close-button {
            color: var(--text-light);
            font-size: 2rem;
            font-weight: bold;
            position: absolute;
            top: 15px;
            right: 25px;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close-button:hover,
        .close-button:focus {
            color: var(--primary-cyan);
            text-decoration: none;
        }

        .modal-headline {
            font-size: clamp(1.8rem, 3vw, 2.5rem);
            color: var(--text-primary);
            margin-bottom: var(--space-sm);
            text-align: center;
        }

        .modal-subheadline {
            font-size: 1.1rem;
            color: var(--text-secondary);
            margin-bottom: var(--space-lg);
            text-align: center;
        }

        .contact-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
        }

        .contact-card {
            background: var(--bg-card);
            padding: var(--space-md);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(66, 184, 221, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 150px;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
            border-color: var(--primary-cyan);
        }

        .contact-card .icon {
            font-size: 2.5rem;
            color: var(--primary-cyan);
            margin-bottom: var(--space-sm);
        }

        .contact-card h4 {
            font-size: 1.2rem;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .contact-card p {
            font-size: 0.9rem;
            color: var(--text-light);
        }

        /* ============================================
           HERO SECTION
           ============================================ */
        #hero {
            padding-top: 160px;
            padding-bottom: var(--space-xl);
            background: radial-gradient(ellipse at top right, rgba(23, 184, 221, 0.1) 0%, transparent 50%),
                        radial-gradient(ellipse at bottom left, rgba(35, 166, 199, 0.1) 0%, transparent 50%);
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-xl);
            align-items: center;
        }

        .hero-text h1 {
            margin-bottom: var(--space-md);
        }

        .hero-text .subheadline {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: var(--space-lg);
        }

        .hero-stats {
            display: flex;
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
            font-size: 0.875rem;
            color: var(--text-light);
        }

        .hero-stats span {
            padding: 8px 16px;
            background: var(--bg-card);
            border-radius: 4px;
            border: 1px solid rgba(66, 184, 221, 0.2);
        }

        .hero-cta {
            display: flex;
            gap: var(--space-md);
            flex-wrap: wrap;
        }

        .hero-image {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px; /* Ensure height for animation */
        }

        /* Automation Animation */
        .automation-animation {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-secondary) 100%);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .automation-animation::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 150px;
            height: 150px;
            background: var(--primary-cyan);
            border-radius: 50%;
            opacity: 0.1;
            filter: blur(40px);
        }

        .process-node {
            position: absolute;
            width: 50px;
            height: 50px;
            background: var(--primary-cyan);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            font-weight: bold;
            font-size: 1.2rem;
            box-shadow: 0 0 15px var(--primary-cyan);
            animation: pulse 2s infinite alternate;
        }

        .node-1 { top: 20%; left: 20%; animation-delay: 0s; }
        .node-2 { top: 20%; right: 20%; animation-delay: 0.5s; }
        .node-3 { bottom: 20%; left: 20%; animation-delay: 1s; }
        .node-4 { bottom: 20%; right: 20%; animation-delay: 1.5s; }

        .automation-line {
            position: absolute;
            background: var(--primary-cyan);
            opacity: 0.7;
            animation: flow 4s linear infinite;
        }

        .line-1 {
            top: 20%; left: 25%;
            width: 50%; height: 4px;
            transform-origin: left center;
            animation-delay: 0s;
        }
        .line-2 {
            top: 25%; right: 20%;
            width: 4px; height: 50%;
            transform-origin: top center;
            animation-delay: 1s;
        }
        .line-3 {
            bottom: 20%; left: 25%;
            width: 50%; height: 4px;
            transform-origin: left center;
            animation: flow-reverse 4s linear infinite;
            animation-delay: 2s;
        }
        .line-4 {
            top: 25%; left: 20%;
            width: 4px; height: 50%;
            transform-origin: top center;
            animation: flow-reverse 4s linear infinite;
            animation-delay: 3s;
        }


        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(1.05); opacity: 0.8; }
        }

        @keyframes flow {
            0% { width: 0%; }
            50% { width: 100%; }
            100% { width: 0%; }
        }

        @keyframes flow-reverse {
            0% { width: 100%; }
            50% { width: 0%; }
            100% { width: 100%; }
        }

        /* ============================================
           BLOG/INSIGHTS SECTION
           ============================================ */
        #insights {
            background: var(--bg-secondary);
        }

        .insights-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }

        .insights-header p {
            margin-top: var(--space-sm);
            color: var(--text-light);
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--space-lg);
        }

        .insight-card {
            background: var(--bg-card);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(66, 184, 221, 0.1);
        }

        .insight-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.5);
            border-color: rgba(66, 184, 221, 0.3);
        }

        .insight-image {
            height: 200px;
            background: linear-gradient(135deg, var(--bg-glass) 0%, var(--bg-transparent) 100%);
            position: relative;
            overflow: hidden;
        }

        .insight-image::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, var(--primary-cyan) 0%, transparent 70%);
            opacity: 0.2;
        }

        .category-tag {
            position: absolute;
            top: 20px;
            left: 20px;
            background: var(--primary-cyan);
            color: #000;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .insight-content {
            padding: var(--space-md);
        }

        .insight-content h3 {
            margin-bottom: var(--space-sm);
            color: var(--text-primary);
        }

        .insight-content p {
            font-size: 1rem;
            color: var(--text-light);
            margin-bottom: var(--space-md);
        }

        .insight-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: var(--text-light);
        }

        .read-more {
            color: var(--primary-cyan);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .read-more:hover {
            text-shadow: var(--glow-subtle);
        }

        /* ============================================
           STORY SECTION
           ============================================ */
        #story {
            background: var(--bg-primary);
            overflow: hidden;
        }

        .story-container {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: var(--space-xl);
            align-items: center;
        }

        .story-image {
            position: relative;
        }

        .story-visual {
            width: 100%;
            height: 400px;
            background: var(--bg-card);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
        }

        .story-visual::before {
            content: '';
            position: absolute;
            inset: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(23, 184, 221, 0.05) 10px,
                rgba(23, 184, 221, 0.05) 20px
            );
        }

        .story-label {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: var(--primary-cyan);
            color: #000;
            padding: 8px 20px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .story-content h2 {
            margin-bottom: var(--space-sm);
        }

        .story-content h3 {
            margin-bottom: var(--space-md);
            color: var(--primary-cyan);
        }

        .story-content p {
            margin-bottom: var(--space-md);
            color: var(--text-secondary);
        }

        /* ============================================
           CASE STUDY SECTION
           ============================================ */
        #case-study {
            background: var(--bg-secondary);
        }

        .case-study-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }

        .case-study-header p {
            margin-top: var(--space-sm);
            color: var(--text-light);
        }

        .featured-case {
            background: var(--bg-card);
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid rgba(66, 184, 221, 0.1);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .case-badge {
            background: var(--success-green);
            color: #000;
            padding: var(--space-xs) var(--space-md);
            display: inline-block;
            border-radius: 0 0 8px 8px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: var(--space-md);
        }

        .case-content {
            padding: var(--space-lg);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-xl);
            align-items: center;
        }

        .case-details h3 {
            font-size: 2rem;
            margin-bottom: var(--space-md);
            color: var(--text-primary);
        }

        .case-details p {
            margin-bottom: var(--space-md);
            color: var(--text-secondary);
        }

        .case-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--space-md);
            margin: var(--space-lg) 0;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-cyan);
            line-height: 1;
        }

        .metric-label {
            font-size: 0.875rem;
            color: var(--text-light);
            margin-top: var(--space-xs);
        }

        .case-visual {
            background: linear-gradient(135deg, var(--bg-glass) 0%, var(--bg-transparent) 100%);
            height: 400px;
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(66, 184, 221, 0.2);
        }

        .case-visual::before {
            content: '';
            position: absolute;
            inset: 0;
            background-image:
                linear-gradient(rgba(23, 184, 221, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(23, 184, 221, 0.1) 1px, transparent 1px);
            background-size: 30px 30px;
        }

        /* ============================================
           TESTIMONIALS SECTION (MOSAIC)
           ============================================ */
        #testimonials {
            background: #1a1a1a;
            overflow: hidden;
            position: relative;
        }

        .testimonials-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }

        .testimonials-header h2 {
            color: var(--text-primary);
        }

        .testimonials-header p {
            color: rgba(255, 255, 255, 0.7);
            margin-top: var(--space-sm);
        }

        .testimonial-mosaic {
            position: relative;
            height: 700px;
            overflow: hidden;
            max-width: 1400px;
            margin: 0 auto;
        }

        .mosaic-container {
            display: flex;
            gap: 20px;
            animation: scrollMosaic 50s linear infinite;
            width: max-content;
            padding: 20px 0;
        }

        .mosaic-container:hover {
            animation-play-state: paused;
        }

        .mosaic-column {
            display: flex;
            flex-direction: column;
            gap: 20px;
            width: 360px;
        }

        .mosaic-column:nth-child(even) {
            margin-top: 60px;
        }

        .mosaic-column:nth-child(3n) {
            margin-top: 30px;
        }

        @keyframes scrollMosaic {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        .testimonial-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 24px;
            border-radius: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .testimonial-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.08);
        }

        .testimonial-card.small {
            padding: 20px;
            min-height: 120px;
        }

        .testimonial-card.medium {
            min-height: 200px;
        }

        .testimonial-card.large {
            padding: 32px;
            min-height: 280px;
        }

        .testimonial-card.video {
            background: #000;
            position: relative;
            min-height: 240px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-play {
            width: 60px;
            height: 60px;
            background: rgba(195, 136, 32, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-play:hover {
            transform: scale(1.1);
            background: var(--warning-orange);
        }

        .video-play::after {
            content: '';
            width: 0;
            height: 0;
            border-left: 18px solid var(--text-primary);
            border-top: 12px solid transparent;
            border-bottom: 12px solid transparent;
            margin-left: 4px;
        }

        .testimonial-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .testimonial-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--primary-cyan);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            font-weight: 600;
            font-size: 18px;
        }

        .testimonial-info {
            flex: 1;
        }

        .testimonial-author {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .testimonial-role {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.6);
        }

        .testimonial-text {
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.6;
        }

        .testimonial-quote {
            font-size: 1.25rem;
            font-weight: 300;
            font-style: italic;
        }

        .testimonial-stat {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--warning-orange);
            margin-bottom: 8px;
        }

        .testimonial-metric {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
        }

        .testimonial-accent {
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
        }

        .testimonial-card.gold-accent .testimonial-accent {
            background: var(--warning-orange);
        }

        .testimonial-card.green-accent .testimonial-accent {
            background: var(--success-green);
        }

        .fade-edge-left,
        .fade-edge-right {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 120px;
            pointer-events: none;
            z-index: 10;
        }

        .fade-edge-left {
            left: 0;
            background: linear-gradient(to right, #1a1a1a, transparent);
        }

        .fade-edge-right {
            right: 0;
            background: linear-gradient(to left, #1a1a1a, transparent);
        }

        /* ============================================
           FAQ SECTION
           ============================================ */
        #faq {
            background: var(--bg-primary);
        }

        .faq-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }

        .faq-header p {
            margin-top: var(--space-sm);
            color: var(--text-light);
        }

        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            background: var(--bg-card);
            border-radius: 8px;
            margin-bottom: var(--space-md);
            border: 1px solid rgba(66, 184, 221, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-item:hover {
            border-color: rgba(66, 184, 221, 0.3);
        }

        .faq-question {
            padding: var(--space-md);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .faq-question:hover {
            background: rgba(66, 184, 221, 0.05);
        }

        .faq-question h3 {
            font-size: 1.125rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .faq-icon {
            color: var(--primary-cyan);
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }

        .faq-item.active .faq-icon {
            transform: rotate(45deg);
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .faq-item.active .faq-answer {
            max-height: 300px; /* Adjust as needed for content */
        }

        .faq-answer p {
            padding: 0 var(--space-md) var(--space-md);
            color: var(--text-secondary);
        }

        /* ============================================
           FOOTER
           ============================================ */
        footer {
            background: var(--bg-secondary);
            color: var(--text-secondary);
            padding: var(--space-xl) 0 var(--space-lg);
            border-top: 1px solid rgba(66, 184, 221, 0.1);
        }

        .footer-content {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 2fr;
            gap: var(--space-xl);
            margin-bottom: var(--space-xl);
        }

        .footer-brand h3 {
            font-size: 1.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-cyan) 0%, var(--dark-cyan) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--space-sm);
        }

        .footer-section h4 {
            color: var(--primary-cyan);
            margin-bottom: var(--space-md);
            font-size: 1.125rem;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section li {
            margin-bottom: var(--space-sm);
        }

        .footer-section a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: var(--primary-cyan);
        }

        .newsletter-form {
            display: flex;
            flex-direction: column;
            gap: var(--space-sm);
        }

        .newsletter-input {
            padding: 12px 16px;
            border: 1px solid rgba(66, 184, 221, 0.2);
            background: var(--bg-card);
            border-radius: 6px;
            font-size: 1rem;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .newsletter-input:focus {
            outline: none;
            border-color: var(--primary-cyan);
            box-shadow: 0 0 0 2px rgba(23, 184, 221, 0.1);
        }

        .newsletter-btn {
            background: var(--primary-cyan);
            color: #000;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .newsletter-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(23, 184, 221, 0.3);
        }

        .social-links {
            display: flex;
            gap: var(--space-sm);
            margin-top: var(--space-md);
        }

        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: var(--bg-transparent);
            color: var(--primary-cyan);
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(66, 184, 221, 0.2);
        }

        .social-links a:hover {
            background: var(--primary-cyan);
            color: #000;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 184, 221, 0.3);
        }

        .footer-bottom {
            text-align: center;
            padding-top: var(--space-lg);
            border-top: 1px solid rgba(66, 184, 221, 0.1);
            color: var(--text-light);
            font-size: 0.875rem;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .insights-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .footer-content {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .mobile-menu {
                display: block;
            }

            .nav-links {
                display: none;
                position: absolute;
                top: 80px;
                left: 0;
                right: 0;
                background: rgba(26, 26, 35, 0.98);
                flex-direction: column;
                padding: var(--space-md);
                transform: translateX(0);
                border-bottom: 1px solid rgba(66, 184, 221, 0.1);
            }

            .nav-links.active {
                display: flex;
            }

            .nav-cta {
                display: none;
            }

            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .hero-image {
                height: 300px; /* Adjust for smaller screens */
            }

            .hero-cta {
                justify-content: center;
            }

            .insights-grid {
                grid-template-columns: 1fr;
            }

            .story-container {
                grid-template-columns: 1fr;
            }

            .case-content {
                grid-template-columns: 1fr;
            }

            .case-metrics {
                grid-template-columns: repeat(3, 1fr);
            }

            .footer-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .social-links {
                justify-content: center;
            }

            .modal-content {
                padding: var(--space-md);
            }

            .contact-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- ============================================
         HEADER & NAVIGATION
         ============================================ -->
    <header>
        <nav class="container">
            <a href="#hero" class="logo">DENIS</a>
            <ul class="nav-links" id="navMenu">
                <li><a href="#hero">Home</a></li>
                <li><a href="javascript:void(0);">Services</a></li> <!-- Changed href -->
                <li><a href="javascript:void(0);">Portfolio</a></li> <!-- Changed href -->
                <li><a href="#testimonials">Reviews</a></li>
                <li><a href="#faq">FAQ</a></li>
            </ul>
            <div class="nav-cta">
                <a href="#" class="btn btn-primary" id="getInTouchNavBtn">Get In Touch</a>
            </div>
            <button class="mobile-menu" id="mobileMenu">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </nav>
    </header>

    <!-- Floating Get In Touch Button -->
    <a href="#" class="floating-contact-btn" id="floatingContactBtn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle-code"><path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"/><path d="m10 10-2 2 2 2"/><path d="m14 14 2-2-2-2"/></svg>
        Get In Touch
    </a>

    <!-- Contact Modal -->
    <div id="contactModal" class="modal">
        <div class="modal-content">
            <span class="close-button">&times;</span>
            <h2 class="modal-headline">We’d Love to Hear From You!</h2>
            <p class="modal-subheadline">Choose how you'd like to connect:</p>
            <div class="contact-options">
                <a href="mailto:<EMAIL>" class="contact-card">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon lucide lucide-mail"><rect width="20" height="16" x="2" y="4" rx="2"/><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/></svg>
                    <h4>Send an Email</h4>
                    <p>Reach out directly</p>
                </a>
                <a href="https://calendly.com/your-scheduling-link" target="_blank" class="contact-card">
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon lucide lucide-calendar-check"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"/><line x1="16" x2="16" y1="2" y2="6"/><line x1="8" x2="8" y1="2" y2="6"/><line x1="3" x2="21" y1="10" y2="10"/><path d="m9 16 2 2 4-4"/></svg>
                    <h4>Book a 30-min Call</h4>
                    <p>Schedule a free consultation</p>
                </a>
                <a href="javascript:void(0);" class="contact-card" id="openContactForm"> <!-- Changed href -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon lucide lucide-form-input"><rect width="20" height="12" x="2" y="6" rx="2"/><path d="M12 12h.01"/><path d="M17 12h.01"/><path d="M7 12h.01"/></svg>
                    <h4>Contact Form</h4>
                    <p>Fill out our online form</p>
                </a>
            </div>
        </div>
    </div>

    <!-- ============================================
         HERO SECTION
         ============================================ -->
    <section id="hero" class="section">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>Turn Your Business Processes Into A <span class="highlight">Sales Magnet</span> With <span class="highlight">AI-Powered Automations</span></h1>
                    <p class="subheadline">I've helped businesses achieve extraordinary growth through strategic AI automation.</p>

                    <div class="hero-stats">
                        <span>AI Automation Specialist</span>
                        <span>Process Optimization</span>
                        <span>4+ Years Experience</span>
                    </div>

                    <div class="hero-cta">
                        <a href="#case-study" class="btn btn-primary">View Case Studies</a>
                        <a href="https://calendly.com/your-scheduling-link" target="_blank" class="btn btn-secondary">Book a Call</a>
                    </div>
                </div>

                <div class="hero-image">
                    <div class="automation-animation">
                        <div class="process-node node-1">A</div>
                        <div class="process-node node-2">B</div>
                        <div class="process-node node-3">C</div>
                        <div class="process-node node-4">D</div>
                        <div class="automation-line line-1"></div>
                        <div class="automation-line line-2"></div>
                        <div class="automation-line line-3"></div>
                        <div class="automation-line line-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ============================================
         INSIGHTS SECTION
         ============================================ -->
    <section id="insights" class="section">
        <div class="container">
            <div class="insights-header">
                <h2>Latest AI & Automation Insights</h2>
                <p>Practical strategies and tutorials from my experience</p>
            </div>

            <div class="insights-grid">
                <article class="insight-card">
                    <div class="insight-image">
                        <span class="category-tag">AI Workflows</span>
                    </div>
                    <div class="insight-content">
                        <h3>Automating Customer Support with AI Chatbots</h3>
                        <p>Learn how to implement AI-powered chatbots to handle common customer queries, improving response times and satisfaction.</p>
                        <div class="insight-meta">
                            <span>7 min read</span>
                            <a href="javascript:void(0);" class="read-more">Read More →</a> <!-- Changed href -->
                        </div>
                    </div>
                </article>

                <article class="insight-card">
                    <div class="insight-image">
                        <span class="category-tag">Process Optimization</span>
                    </div>
                    <div class="insight-content">
                        <h3>Streamlining Onboarding with Automation</h3>
                        <p>Discover how automated workflows can transform your employee or client onboarding process, saving time and ensuring consistency.</p>
                        <div class="insight-meta">
                            <span>9 min read</span>
                            <a href="javascript:void(0);" class="read-more">Read More →</a> <!-- Changed href -->
                        </div>
                    </div>
                </article>

                <article class="insight-card">
                    <div class="insight-image">
                        <span class="category-tag">Sales Automation</span>
                    </div>
                    <div class="insight-content">
                        <h3>Leveraging AI for Lead Generation & Nurturing</h3>
                        <p>Explore advanced AI techniques to identify high-quality leads and automate personalized nurturing sequences for higher conversions.</p>
                        <div class="insight-meta">
                            <span>6 min read</span>
                            <a href="javascript:void(0);" class="read-more">Read More →</a> <!-- Changed href -->
                        </div>
                    </div>
                </article>
            </div>
        </div>
    </section>

    <!-- ============================================
         STORY SECTION
         ============================================ -->
    <section id="story" class="section">
        <div class="container">
            <div class="story-container">
                <div class="story-image">
                    <div class="story-visual">
                        <span class="story-label">AI Automation Strategist</span>
                    </div>
                </div>

                <div class="story-content">
                    <h2>The Journey to Exponential Growth Through Automation</h2>
                    <h3>From manual processes to powering global business efficiency</h3>

                    <p>Starting with just determination and a laptop, I've built my expertise in optimizing business operations through intelligent automation. What began as identifying inefficiencies has evolved into designing comprehensive AI-powered systems that deliver tangible results.</p>

                    <p>Today, I help businesses worldwide achieve the kind of efficiency and growth that transforms their future, enabling them to focus on innovation while automations handle the rest.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- ============================================
         CASE STUDY SECTION
         ============================================ -->
    <section id="case-study" class="section">
        <div class="container">
            <div class="case-study-header">
                <h2>Transformative Results</h2>
                <p>See how I've helped businesses achieve extraordinary growth</p>
            </div>

            <div class="featured-case">
                <span class="case-badge">Featured Case Study</span>

                <div class="case-content">
                    <div class="case-details">
                        <h3>Fokas LLC: Social Media Automation</h3>
                        <p>From 500 to 16,500 monthly impressions in just 4 months. This luxury service company saw explosive growth through my comprehensive AI-powered social media strategy combining technical optimization, AI-driven content creation, and strategic automation.</p>

                        <div class="case-metrics">
                            <div class="metric">
                                <div class="metric-value">120%</div>
                                <div class="metric-label">Audience Growth</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">4 Months</div>
                                <div class="metric-label">Time to Results</div>
                            </div>
                            <div class="metric">
                                <div class="metric-value">16.5K</div>
                                <div class="metric-label">Monthly Impressions</div>
                            </div>
                        </div>

                        <a href="javascript:void(0);" class="btn btn-primary">Read Full Case Study</a> <!-- Changed href -->
                    </div>

                    <div class="case-visual">
                        <!-- Analytics dashboard visualization -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ============================================
         TESTIMONIALS SECTION (MOSAIC)
         ============================================ -->
    <section id="testimonials" class="section">
        <div class="container">
            <div class="testimonials-header">
                <h2>What People Are Saying</h2>
                <p>Real results from real clients</p>
            </div>
        </div>

        <div class="testimonial-mosaic">
            <div class="fade-edge-left"></div>
            <div class="fade-edge-right"></div>

            <div class="mosaic-container">
                <!-- Column 1 -->
                <div class="mosaic-column">
                    <div class="testimonial-card video">
                        <div class="video-play"></div>
                        <div style="position: absolute; bottom: 20px; left: 20px;">
                            <div class="testimonial-author">John D.</div>
                            <div class="testimonial-role">CEO & Founder</div>
                        </div>
                    </div>

                    <div class="testimonial-card large gold-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-text testimonial-quote">
                            "Since implementing Denis's automations, our efficiency has skyrocketed. He doesn't just do automation, he transforms businesses."
                        </div>
                        <div style="margin-top: 20px;">
                            <div class="testimonial-author">Maria Rodriguez</div>
                            <div class="testimonial-role">CEO Digital Agency</div>
                        </div>
                    </div>

                    <div class="testimonial-card small">
                        <div class="testimonial-stat">300%</div>
                        <div class="testimonial-metric">process efficiency in 3 months!</div>
                        <div style="margin-top: 12px;">
                            <div class="testimonial-author">James Wilson</div>
                        </div>
                    </div>
                </div>

                <!-- Column 2 -->
                <div class="mosaic-column">
                    <div class="testimonial-card medium green-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">AT</div>
                            <div class="testimonial-info">
                                <div class="testimonial-author">Alex Turner</div>
                                <div class="testimonial-role">Agency Owner</div>
                            </div>
                        </div>
                        <div class="testimonial-text">
                            "Best investment in our operations ever. The AI workflows alone are worth 10x what we paid."
                        </div>
                    </div>

                    <div class="testimonial-card large">
                        <div class="testimonial-text">
                            "Just implemented Denis's AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof 🚀"
                        </div>
                        <div style="margin-top: 20px;">
                            <div class="testimonial-author">Emma Davis</div>
                            <div class="testimonial-role">Marketing Director</div>
                        </div>
                    </div>

                    <div class="testimonial-card video">
                        <div class="video-play"></div>
                        <div style="position: absolute; bottom: 20px; left: 20px;">
                            <div class="testimonial-author">Lisa Martinez</div>
                            <div class="testimonial-role">Startup Founder</div>
                        </div>
                    </div>
                </div>

                <!-- Column 3 -->
                <div class="mosaic-column">
                    <div class="testimonial-card small gold-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-text">
                            "1700% growth sounds impossible until you work with Denis. He delivered exactly that for our startup's sales pipeline!"
                        </div>
                        <div style="margin-top: 12px;">
                            <div class="testimonial-author">Michael Brown</div>
                        </div>
                    </div>

                    <div class="testimonial-card large">
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">JL</div>
                            <div class="testimonial-info">
                                <div class="testimonial-author">Jennifer Lee</div>
                                <div class="testimonial-role">Local Business</div>
                            </div>
                        </div>
                        <div class="testimonial-text">
                            "Automated our lead qualification. 300% faster response times. Our sales team is closing deals quicker than ever."
                        </div>
                    </div>

                    <div class="testimonial-card medium green-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-stat">10x</div>
                        <div class="testimonial-metric">Operational Efficiency. Best investment in our business ever!</div>
                        <div style="margin-top: 16px;">
                            <div class="testimonial-author">Sarah Chen</div>
                            <div class="testimonial-role">Business Owner</div>
                        </div>
                    </div>
                </div>

                <!-- Column 4 -->
                <div class="mosaic-column">
                    <div class="testimonial-card medium">
                        <div class="testimonial-text testimonial-quote">
                            "an unmatched skill in automation"
                        </div>
                        <div style="margin-top: 16px;">
                            <div class="testimonial-author">Patricia Williams</div>
                            <div class="testimonial-role">Creative Director</div>
                        </div>
                    </div>

                    <div class="testimonial-card small gold-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">KT</div>
                            <div class="testimonial-info">
                                <div class="testimonial-author">Kevin Taylor</div>
                                <div class="testimonial-role">B2B SaaS Platform</div>
                            </div>
                        </div>
                        <div class="testimonial-text">
                            "Automated our data entry and reporting, saving countless hours."
                        </div>
                    </div>

                    <div class="testimonial-card large green-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-text">
                            "Just implemented Denis's AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof."
                        </div>
                        <div style="margin-top: 20px;">
                            <div class="testimonial-author">Robert Anderson</div>
                            <div class="testimonial-role">VP of Sales</div>
                        </div>
                    </div>
                </div>

                <!-- Duplicate columns for continuous scroll -->
                <div class="mosaic-column">
                    <div class="testimonial-card video">
                        <div class="video-play"></div>
                        <div style="position: absolute; bottom: 20px; left: 20px;">
                            <div class="testimonial-author">John D.</div>
                            <div class="testimonial-role">CEO & Founder</div>
                        </div>
                    </div>

                    <div class="testimonial-card large gold-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-text testimonial-quote">
                            "Since implementing Denis's automations, our efficiency has skyrocketed. He doesn't just do automation, he transforms businesses."
                        </div>
                        <div style="margin-top: 20px;">
                            <div class="testimonial-author">Maria Rodriguez</div>
                            <div class="testimonial-role">CEO Digital Agency</div>
                        </div>
                    </div>

                    <div class="testimonial-card small">
                        <div class="testimonial-stat">300%</div>
                        <div class="testimonial-metric">process efficiency in 3 months!</div>
                        <div style="margin-top: 12px;">
                            <div class="testimonial-author">James Wilson</div>
                        </div>
                    </div>
                </div>

                <div class="mosaic-column">
                    <div class="testimonial-card medium green-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">AT</div>
                            <div class="testimonial-info">
                                <div class="testimonial-author">Alex Turner</div>
                                <div class="testimonial-role">Agency Owner</div>
                            </div>
                        </div>
                        <div class="testimonial-text">
                            "Best investment in our operations ever. The AI workflows alone are worth 10x what we paid."
                        </div>
                    </div>

                    <div class="testimonial-card large">
                        <div class="testimonial-text">
                            "Just implemented Denis's AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof 🚀"
                        </div>
                        <div style="margin-top: 20px;">
                            <div class="testimonial-author">Emma Davis</div>
                            <div class="testimonial-role">Marketing Director</div>
                        </div>
                    </div>

                    <div class="testimonial-card video">
                        <div class="video-play"></div>
                        <div style="position: absolute; bottom: 20px; left: 20px;">
                            <div class="testimonial-author">Lisa Martinez</div>
                            <div class="testimonial-role">Startup Founder</div>
                        </div>
                    </div>
                </div>

                <div class="mosaic-column">
                    <div class="testimonial-card small gold-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-text">
                            "1700% growth sounds impossible until you work with Denis. He delivered exactly that for our startup's sales pipeline!"
                        </div>
                        <div style="margin-top: 12px;">
                            <div class="testimonial-author">Michael Brown</div>
                        </div>
                    </div>

                    <div class="testimonial-card large">
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">JL</div>
                            <div class="testimonial-info">
                                <div class="testimonial-author">Jennifer Lee</div>
                                <div class="testimonial-role">Local Business</div>
                            </div>
                        </div>
                        <div class="testimonial-text">
                            "Automated our lead qualification. 300% faster response times. Our sales team is closing deals quicker than ever."
                        </div>
                    </div>

                    <div class="testimonial-card medium green-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-stat">10x</div>
                        <div class="testimonial-metric">Operational Efficiency. Best investment in our business ever!</div>
                        <div style="margin-top: 16px;">
                            <div class="testimonial-author">Sarah Chen</div>
                            <div class="testimonial-role">Business Owner</div>
                        </div>
                    </div>
                </div>

                <div class="mosaic-column">
                    <div class="testimonial-card medium">
                        <div class="testimonial-text testimonial-quote">
                            "an unmatched skill in automation"
                        </div>
                        <div style="margin-top: 16px;">
                            <div class="testimonial-author">Patricia Williams</div>
                            <div class="testimonial-role">Creative Director</div>
                        </div>
                    </div>

                    <div class="testimonial-card small gold-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-header">
                            <div class="testimonial-avatar">KT</div>
                            <div class="testimonial-info">
                                <div class="testimonial-author">Kevin Taylor</div>
                                <div class="testimonial-role">B2B SaaS Platform</div>
                            </div>
                        </div>
                        <div class="testimonial-text">
                            "Automated our data entry and reporting, saving countless hours."
                        </div>
                    </div>

                    <div class="testimonial-card large green-accent">
                        <div class="testimonial-accent"></div>
                        <div class="testimonial-text">
                            "Just implemented Denis's AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof."
                        </div>
                        <div style="margin-top: 20px;">
                            <div class="testimonial-author">Robert Anderson</div>
                            <div class="testimonial-role">VP of Sales</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ============================================
         FAQ SECTION
         ============================================ -->
    <section id="faq" class="section">
        <div class="container">
            <div class="faq-header">
                <h2>Frequently Asked Questions</h2>
                <p>Everything you need to know before getting started</p>
            </div>

            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What types of business processes can be automated with AI?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>AI automation can transform a wide range of processes, including customer support (chatbots, ticket routing), sales (lead qualification, personalized outreach), marketing (content generation, email campaigns), data entry, reporting, HR onboarding, and much more. If a process is repetitive and rule-based, it's a strong candidate for automation.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>How do you determine which automations are right for my business?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>My process begins with a detailed discovery phase where I analyze your current workflows, identify bottlenecks, and pinpoint areas with the highest potential for efficiency gains and ROI through automation. We'll prioritize solutions that align with your business goals and deliver the most significant impact.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What is your typical project timeline and availability?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Project timelines vary depending on the complexity and scope of the automation. Smaller projects might take a few weeks, while comprehensive system integrations could span several months. I maintain a flexible schedule to accommodate new clients and ensure timely delivery. It's best to book a call to discuss your specific needs and my current availability.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>Do you offer ongoing support or maintenance for implemented automations?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, I offer various levels of post-implementation support and maintenance plans to ensure your automations continue to run smoothly and efficiently. This can include monitoring, troubleshooting, performance optimization, and updates as your business needs evolve. My goal is to ensure long-term success and value from your AI investments.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question">
                        <h3>What kind of results can I expect from AI automation?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Clients typically experience significant improvements in operational efficiency, reduced manual errors, faster response times, increased employee productivity, and enhanced customer satisfaction. For sales and marketing, this often translates to higher lead conversion rates and a stronger sales pipeline. The specific ROI will depend on the processes automated, but the goal is always measurable business growth.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- ============================================
         FOOTER
         ============================================ -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <h3>DENIS</h3>
                    <p>AI Automation Strategist & Business Efficiency Expert</p>
                    <p>Helping businesses achieve exponential growth through strategic AI automation. Transform your operations.</p>
                    <div class="social-links">
                        <a href="javascript:void(0);" title="LinkedIn">in</a> <!-- Changed href -->
                        <a href="javascript:void(0);" title="Twitter">X</a> <!-- Changed href -->
                        <a href="javascript:void(0);" title="GitHub">gh</a> <!-- Changed href -->
                        <a href="javascript:void(0);" title="Email">@</a> <!-- Changed href -->
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#hero">Home</a></li>
                        <li><a href="javascript:void(0);">Services</a></li> <!-- Changed href -->
                        <li><a href="#case-study">Case Studies</a></li>
                        <li><a href="javascript:void(0);">About</a></li> <!-- Changed href -->
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul>
                        <li><a href="javascript:void(0);">Automation Blog</a></li> <!-- Changed href -->
                        <li><a href="javascript:void(0);">AI Workflows</a></li> <!-- Changed href -->
                        <li><a href="javascript:void(0);">Free Tools</a></li> <!-- Changed href -->
                        <li><a href="javascript:void(0);">Privacy Policy</a></li> <!-- Changed href -->
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Stay Updated</h4>
                    <p>Get weekly automation tips and AI strategies delivered to your inbox.</p>
                    <form class="newsletter-form">
                        <input type="email" class="newsletter-input" placeholder="Your email" required>
                        <button type="submit" class="newsletter-btn">Subscribe</button>
                    </form>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 DENIS. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile Menu Toggle
        const mobileMenu = document.getElementById('mobileMenu');
        const navMenu = document.getElementById('navMenu');

        mobileMenu.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            const spans = mobileMenu.querySelectorAll('span');
            spans[0].style.transform = navMenu.classList.contains('active') ? 'rotate(45deg) translate(5px, 5px)' : '';
            spans[1].style.opacity = navMenu.classList.contains('active') ? '0' : '1';
            spans[2].style.transform = navMenu.classList.contains('active') ? 'rotate(-45deg) translate(5px, -5px)' : '';
        });

        // FAQ Accordion
        document.querySelectorAll('.faq-question').forEach(item => {
            item.addEventListener('click', event => {
                const faqItem = item.closest('.faq-item');
                faqItem.classList.toggle('active');
            });
        });

        // Contact Modal Logic
        const contactModal = document.getElementById('contactModal');
        const floatingContactBtn = document.getElementById('floatingContactBtn');
        const getInTouchNavBtn = document.getElementById('getInTouchNavBtn');
        const closeButton = document.querySelector('.modal .close-button');
        const openContactForm = document.getElementById('openContactForm');

        function openModal() {
            contactModal.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent scrolling background
        }

        function closeModal() {
            contactModal.classList.remove('active');
            document.body.style.overflow = ''; // Restore scrolling
        }

        floatingContactBtn.addEventListener('click', (e) => {
            e.preventDefault();
            openModal();
        });

        getInTouchNavBtn.addEventListener('click', (e) => {
            e.preventDefault();
            openModal();
        });

        closeButton.addEventListener('click', closeModal);

        contactModal.addEventListener('click', (e) => {
            if (e.target === contactModal) {
                closeModal();
            }
        });

        openContactForm.addEventListener('click', (e) => {
            e.preventDefault();
            closeModal();
            // Scroll to a hypothetical contact form section if it existed
            // Example: document.getElementById('contact-form-section').scrollIntoView({ behavior: 'smooth' });
        });
    </script>
</body>
</html>
