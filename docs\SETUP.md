# Setup Guide - <PERSON>

## 🚀 Initial Project Setup

### Prerequisites
- Node.js 18+ installed
- Git installed
- GitHub account
- Vercel account (free tier)
- Supabase account (free tier)

### 1. Repository Setup

```bash
# Clone the repository
git clone https://github.com/ProDevDenis/MyPortfolio.git
cd MyPortfolio

# Set up branches
git checkout -b develop
git push -u origin develop

# Switch back to main
git checkout main
```

### 2. Next.js Project Initialization

```bash
# Initialize Next.js project
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir

# Install additional dependencies
npm install @supabase/supabase-js
npm install -D @types/node
```

### 3. Environment Configuration

Create environment files:

```bash
# .env.local (for local development)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# .env.example (template for team)
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
```

### 4. Supabase Setup

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Note down URL and anon key

2. **Database Schema Setup**
   ```sql
   -- Contacts table
   CREATE TABLE contacts (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     name TEXT NOT NULL,
     email TEXT NOT NULL,
     message TEXT NOT NULL,
     source TEXT DEFAULT 'website',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Analytics table
   CREATE TABLE analytics (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     page TEXT NOT NULL,
     user_agent TEXT,
     referrer TEXT,
     ip_address INET,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );
   ```

### 5. Vercel Setup

1. **Connect Repository**
   - Go to [vercel.com](https://vercel.com)
   - Import GitHub repository
   - Configure build settings

2. **Environment Variables**
   - Add Supabase credentials
   - Configure for both staging and production

### 6. GitHub Actions Setup

Create `.github/workflows/ci.yml`:

```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      - run: npm ci
      - run: npm run build
      - run: npm run test
```

### 7. Development Workflow

```bash
# Start development server
npm run dev

# Run tests
npm run test

# Build for production
npm run build

# Start production server
npm start
```

### 8. Branch Protection Rules

Configure in GitHub repository settings:
- Require pull request reviews
- Require status checks to pass
- Require branches to be up to date
- Restrict pushes to main branch

### 9. Verification Steps

- [ ] Repository cloned successfully
- [ ] Next.js development server runs
- [ ] Supabase connection works
- [ ] Vercel deployment successful
- [ ] GitHub Actions pipeline runs
- [ ] Branch protection rules active

### 🔧 Troubleshooting

**Common Issues:**
- Node.js version compatibility
- Environment variable configuration
- Supabase connection errors
- Vercel deployment failures

See [Troubleshooting Guide](./TROUBLESHOOTING.md) for detailed solutions.

---
*Setup Guide Version: 1.0.0*
*Last Updated: 2025-01-01*
