/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVOSVMlNUNEZXNrdG9wJTVDcG9vb28lNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNhcHAtcm91dGVyLmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVOSVMlNUNEZXNrdG9wJTVDcG9vb28lNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNlcnJvci1ib3VuZGFyeS5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RFTklTJTVDRGVza3RvcCU1Q3Bvb29vJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbGF5b3V0LXJvdXRlci5qcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0RFTklTJTVDRGVza3RvcCU1Q3Bvb29vJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNjb21wb25lbnRzJTVDbm90LWZvdW5kLWJvdW5kYXJ5LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVOSVMlNUNEZXNrdG9wJTVDcG9vb28lNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVOSVMlNUNEZXNrdG9wJTVDcG9vb28lNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2NvbXBvbmVudHMlNUNzdGF0aWMtZ2VuZXJhdGlvbi1zZWFyY2hwYXJhbXMtYmFpbG91dC1wcm92aWRlci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa09BQWtJO0FBQ2xJLDBPQUFzSTtBQUN0SSx3T0FBcUk7QUFDckksa1BBQTBJO0FBQzFJLHNRQUFvSjtBQUNwSiIsInNvdXJjZXMiOlsid2VicGFjazovL2RlbmlzLWVyYXN0dXMtcG9ydGZvbGlvLz9kM2Q0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcREVOSVNcXFxcRGVza3RvcFxcXFxwb29vb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGFwcC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERFTklTXFxcXERlc2t0b3BcXFxccG9vb29cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcREVOSVNcXFxcRGVza3RvcFxcXFxwb29vb1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERFTklTXFxcXERlc2t0b3BcXFxccG9vb29cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxub3QtZm91bmQtYm91bmRhcnkuanNcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERFTklTXFxcXERlc2t0b3BcXFxccG9vb29cXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxERU5JU1xcXFxEZXNrdG9wXFxcXHBvb29vXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcc3RhdGljLWdlbmVyYXRpb24tc2VhcmNocGFyYW1zLWJhaWxvdXQtcHJvdmlkZXIuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVOSVMlNUNEZXNrdG9wJTVDcG9vb28lNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW5pcy1lcmFzdHVzLXBvcnRmb2xpby8/NjFmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERFTklTXFxcXERlc2t0b3BcXFxccG9vb29cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_Insights__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Insights */ \"(ssr)/./src/components/Insights.tsx\");\n/* harmony import */ var _components_Story__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Story */ \"(ssr)/./src/components/Story.tsx\");\n/* harmony import */ var _components_CaseStudy__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CaseStudy */ \"(ssr)/./src/components/CaseStudy.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Testimonials */ \"(ssr)/./src/components/Testimonials.tsx\");\n/* harmony import */ var _components_FAQ__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/FAQ */ \"(ssr)/./src/components/FAQ.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_ContactModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ContactModal */ \"(ssr)/./src/components/ContactModal.tsx\");\n/* harmony import */ var _components_FloatingContactButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/FloatingContactButton */ \"(ssr)/./src/components/FloatingContactButton.tsx\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/analytics */ \"(ssr)/./src/lib/analytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Track page view\n        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_12__.trackPageView)(\"/\");\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloatingContactButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Insights__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Story__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CaseStudy__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FAQ__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AutomationAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/AutomationAnimation.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AutomationAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction AutomationAnimation() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"automation-animation\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"central-hub\",\n                children: \"AI\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-1\",\n                children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 5,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-2\",\n                children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-3\",\n                children: \"⚡\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-4\",\n                children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-5\",\n                children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-6\",\n                children: \"\\uD83D\\uDD04\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"floating-particles\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AutomationAnimation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CaseStudy.tsx":
/*!**************************************!*\
  !*** ./src/components/CaseStudy.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CaseStudy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction CaseStudy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"case-study\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"case-study-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"case-study-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"case-study-label\",\n                                children: \"Featured Case Study\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 7,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"How Fokas LLC Increased Revenue by 300% with AI Automation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 8,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"case-study-grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"case-study-details\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"challenge\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"The Challenge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Fokas LLC was struggling with manual lead qualification and follow-up processes, losing potential customers due to delayed responses and inconsistent communication.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 15,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"solution\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"The Solution\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Implemented an AI-powered lead qualification system with automated follow-up sequences and intelligent chatbot integration.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"results\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"The Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metrics\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"metric\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-value\",\n                                                                children: \"300%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 35,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-label\",\n                                                                children: \"Revenue Increase\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 36,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                        lineNumber: 34,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"metric\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-value\",\n                                                                children: \"85%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 39,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-label\",\n                                                                children: \"Time Saved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 40,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"metric\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-value\",\n                                                                children: \"24/7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 43,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-label\",\n                                                                children: \"Automated Support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 44,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                        lineNumber: 42,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"case-study-visual\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"case-study-image-placeholder\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCC8 Success Story\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CaseStudy.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ContactModal.tsx":
/*!*****************************************!*\
  !*** ./src/components/ContactModal.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ContactModal() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Modal functionality\n        const modal = document.getElementById(\"contactModal\");\n        const closeBtn = document.querySelector(\".close-button\");\n        const closeModal = ()=>{\n            if (modal) {\n                modal.classList.remove(\"active\");\n                document.body.style.overflow = \"auto\";\n            }\n        };\n        // Close modal when clicking close button\n        if (closeBtn) {\n            closeBtn.addEventListener(\"click\", closeModal);\n        }\n        // Close modal when clicking outside\n        if (modal) {\n            modal.addEventListener(\"click\", (e)=>{\n                if (e.target === modal) {\n                    closeModal();\n                }\n            });\n        }\n        // Close modal with Escape key\n        const handleEscape = (e)=>{\n            if (e.key === \"Escape\") {\n                closeModal();\n            }\n        };\n        document.addEventListener(\"keydown\", handleEscape);\n        return ()=>{\n            if (closeBtn) {\n                closeBtn.removeEventListener(\"click\", closeModal);\n            }\n            if (modal) {\n                modal.removeEventListener(\"click\", closeModal);\n            }\n            document.removeEventListener(\"keydown\", handleEscape);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"contactModal\",\n        className: \"modal\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"modal-content\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"close-button\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"modal-headline\",\n                    children: \"We'd Love to Hear From You!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"modal-subheadline\",\n                    children: \"Choose how you'd like to connect:\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"contact-options\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"mailto:<EMAIL>?subject=AI%20Automation%20Inquiry&body=Hi%20Denis,%0A%0AI'm%20interested%20in%20learning%20more%20about%20your%20AI%20automation%20services.%0A%0ABusiness:%20%0AChallenge:%20%0A%0ALooking%20forward%20to%20hearing%20from%20you!\",\n                            className: \"contact-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    className: \"icon lucide lucide-mail\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            width: \"20\",\n                                            height: \"16\",\n                                            x: \"2\",\n                                            y: \"4\",\n                                            rx: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    children: \"Send an Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Get a detailed response within 24 hours\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://calendly.com/your-scheduling-link\",\n                            target: \"_blank\",\n                            className: \"contact-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    className: \"icon lucide lucide-calendar-check\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            width: \"18\",\n                                            height: \"18\",\n                                            x: \"3\",\n                                            y: \"4\",\n                                            rx: \"2\",\n                                            ry: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"16\",\n                                            x2: \"16\",\n                                            y1: \"2\",\n                                            y2: \"6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"8\",\n                                            x2: \"8\",\n                                            y1: \"2\",\n                                            y2: \"6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"3\",\n                                            x2: \"21\",\n                                            y1: \"10\",\n                                            y2: \"10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"m9 16 2 2 4-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    children: \"Book a 30-min Call\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Free strategy session to discuss your needs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"contact-card\",\n                            id: \"openContactForm\",\n                            onClick: ()=>{\n                                // Close modal and show contact form (to be implemented)\n                                const modal = document.getElementById(\"contactModal\");\n                                if (modal) {\n                                    modal.classList.remove(\"active\");\n                                    document.body.style.overflow = \"auto\";\n                                }\n                            // TODO: Open contact form\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    className: \"icon lucide lucide-form-input\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            width: \"20\",\n                                            height: \"12\",\n                                            x: \"2\",\n                                            y: \"6\",\n                                            rx: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 12h.01\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M17 12h.01\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M7 12h.01\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    children: \"Contact Form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Detailed project inquiry form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ContactModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FAQ.tsx":
/*!********************************!*\
  !*** ./src/components/FAQ.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FAQ)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction FAQ() {\n    const [openIndex, setOpenIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const faqs = [\n        {\n            question: \"What types of business processes can be automated with AI?\",\n            answer: \"I can automate a wide range of processes including customer service (chatbots), lead qualification and nurturing, email marketing sequences, data entry and processing, appointment scheduling, social media management, and sales pipeline management. Essentially, any repetitive task that follows a logical pattern can be automated.\"\n        },\n        {\n            question: \"How do you determine which automations are right for my business?\",\n            answer: \"I start with a comprehensive audit of your current processes to identify bottlenecks and repetitive tasks. Then I analyze your business goals, customer journey, and pain points to prioritize automations that will deliver the highest ROI. We focus on quick wins first, then build more complex systems over time.\"\n        },\n        {\n            question: \"What's the typical timeline for implementing AI automation?\",\n            answer: \"Simple automations like chatbots or email sequences can be implemented in 1-2 weeks. More complex systems involving multiple integrations typically take 4-8 weeks. I work in phases, so you'll see results quickly while we build out the complete system.\"\n        },\n        {\n            question: \"Do you provide ongoing support and maintenance?\",\n            answer: \"Yes, I offer comprehensive support packages that include system monitoring, updates, optimization, and troubleshooting. AI systems need regular fine-tuning to maintain peak performance, and I ensure your automations continue to deliver results as your business grows.\"\n        },\n        {\n            question: \"What kind of results can I expect from AI automation?\",\n            answer: \"Results vary by business, but typical outcomes include 50-80% reduction in manual tasks, 24/7 customer support availability, 200-400% improvement in response times, and 30-60% increase in lead conversion rates. I provide detailed analytics to track ROI and continuously optimize performance.\"\n        },\n        {\n            question: \"How much does AI automation cost?\",\n            answer: \"Investment depends on the complexity and scope of your automation needs. Simple chatbots start around $2,000, while comprehensive automation systems range from $5,000-$20,000. I offer flexible payment plans and the ROI typically pays for the investment within 3-6 months.\"\n        }\n    ];\n    const toggleFAQ = (index)=>{\n        setOpenIndex(openIndex === index ? null : index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"faq\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Everything you need to know about AI automation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"faq-container\",\n                    children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `faq-item ${openIndex === index ? \"active\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"faq-question\",\n                                    onClick: ()=>toggleFAQ(index),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: faq.question\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"faq-icon\",\n                                            children: openIndex === index ? \"−\" : \"+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-answer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: faq.answer\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FAQ.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FloatingContactButton.tsx":
/*!**************************************************!*\
  !*** ./src/components/FloatingContactButton.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FloatingContactButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction FloatingContactButton() {\n    const openContactModal = ()=>{\n        const modal = document.getElementById(\"contactModal\");\n        if (modal) {\n            modal.classList.add(\"active\");\n            document.body.style.overflow = \"hidden\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"floating-contact-btn\",\n        onClick: openContactModal,\n        id: \"floatingContactBtn\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FloatingContactButton.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FloatingContactButton.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            \"Get In Touch\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FloatingContactButton.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FloatingContactButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"footer-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-brand\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"DENIS ERASTUS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 8,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"AI & Business Automation Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 9,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Transforming businesses through intelligent automation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 10,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 7,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-links\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-column\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 15,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"AI Chatbots\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 18,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 17,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"Process Automation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 21,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 20,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"Lead Generation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 24,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 23,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"Workflow Optimization\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 27,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 26,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 16,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-column\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"Company\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#story\",\n                                                            children: \"About\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 36,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 35,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#case-study\",\n                                                            children: \"Case Studies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 39,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#testimonials\",\n                                                            children: \"Testimonials\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 42,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#faq\",\n                                                            children: \"FAQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 45,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 44,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-column\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"mailto:<EMAIL>\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 54,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://calendly.com/your-scheduling-link\",\n                                                            children: \"Book a Call\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#hero\",\n                                                            children: \"Get In Touch\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-bottom\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-social\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        \"aria-label\": \"LinkedIn\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        \"aria-label\": \"Twitter\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-copyright\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"\\xa9 2025 Denis Erastus. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    const openContactModal = ()=>{\n        const modal = document.getElementById(\"contactModal\");\n        if (modal) {\n            modal.classList.add(\"active\");\n            document.body.style.overflow = \"hidden\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"#hero\",\n                    className: \"logo\",\n                    children: \"DENIS ERASTUS\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: `nav-links ${mobileMenuOpen ? \"active\" : \"\"}`,\n                    id: \"navMenu\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#hero\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#insights\",\n                                children: \"Services\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#case-study\",\n                                children: \"Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#testimonials\",\n                                children: \"Reviews\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#faq\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"nav-cta\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"btn btn-primary\",\n                        onClick: openContactModal,\n                        id: \"getInTouchNavBtn\",\n                        children: \"Get In Touch\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"mobile-menu\",\n                    id: \"mobileMenu\",\n                    onClick: toggleMobileMenu,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AutomationAnimation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AutomationAnimation */ \"(ssr)/./src/components/AutomationAnimation.tsx\");\n\n\n\nfunction Hero() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"hero\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-text\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: [\n                                    \"Transform Your Business Into A\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"highlight\",\n                                        children: \"Revenue Machine\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 12,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" With\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"highlight\",\n                                        children: \"AI-Powered Automations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subheadline\",\n                                children: \"I help businesses achieve extraordinary growth by implementing strategic AI automation systems that work 24/7 to drive results.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-stats\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"AI Automation Specialist\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Process Optimization Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"4+ Years Experience\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-cta\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#case-study\",\n                                        className: \"btn btn-primary\",\n                                        children: \"View Case Studies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://calendly.com/your-scheduling-link\",\n                                        target: \"_blank\",\n                                        className: \"btn btn-secondary\",\n                                        children: \"Book a Call\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-image\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomationAnimation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Insights.tsx":
/*!*************************************!*\
  !*** ./src/components/Insights.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Insights)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Insights() {\n    const insights = [\n        {\n            title: \"AI Chatbots That Actually Convert\",\n            description: \"Learn how to build AI chatbots that don't just answer questions, but actively guide prospects through your sales funnel.\",\n            readTime: \"5 min read\",\n            category: \"AI Automation\"\n        },\n        {\n            title: \"Automating Lead Nurturing with AI\",\n            description: \"Discover the exact framework I use to nurture leads automatically while maintaining that personal touch.\",\n            readTime: \"7 min read\",\n            category: \"Lead Generation\"\n        },\n        {\n            title: \"The ROI of Business Process Automation\",\n            description: \"Real numbers from real businesses: How automation delivers measurable returns on investment.\",\n            readTime: \"6 min read\",\n            category: \"Business Strategy\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"insights\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Latest Insights\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Practical strategies and real-world case studies in AI automation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"insights-grid\",\n                    children: insights.map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"insight-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"insight-category\",\n                                    children: insight.category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: insight.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: insight.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"insight-meta\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"read-time\",\n                                            children: insight.readTime\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"read-more\",\n                                            children: \"Read More →\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9JbnNpZ2h0cy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFlLFNBQVNBO0lBQ3RCLE1BQU1DLFdBQVc7UUFDZjtZQUNFQyxPQUFPO1lBQ1BDLGFBQ0U7WUFDRkMsVUFBVTtZQUNWQyxVQUFVO1FBQ1o7UUFDQTtZQUNFSCxPQUFPO1lBQ1BDLGFBQ0U7WUFDRkMsVUFBVTtZQUNWQyxVQUFVO1FBQ1o7UUFDQTtZQUNFSCxPQUFPO1lBQ1BDLGFBQ0U7WUFDRkMsVUFBVTtZQUNWQyxVQUFVO1FBQ1o7S0FDRDtJQUVELHFCQUNFLDhEQUFDQztRQUFRQyxJQUFHO1FBQVdDLFdBQVU7a0JBQy9CLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDRTtzQ0FBRzs7Ozs7O3NDQUNKLDhEQUFDQztzQ0FBRTs7Ozs7Ozs7Ozs7OzhCQUtMLDhEQUFDRjtvQkFBSUQsV0FBVTs4QkFDWlAsU0FBU1csR0FBRyxDQUFDLENBQUNDLFNBQVNDLHNCQUN0Qiw4REFBQ0M7NEJBQW9CUCxXQUFVOzs4Q0FDN0IsOERBQUNDO29DQUFJRCxXQUFVOzhDQUFvQkssUUFBUVIsUUFBUTs7Ozs7OzhDQUNuRCw4REFBQ1c7OENBQUlILFFBQVFYLEtBQUs7Ozs7Ozs4Q0FDbEIsOERBQUNTOzhDQUFHRSxRQUFRVixXQUFXOzs7Ozs7OENBQ3ZCLDhEQUFDTTtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNTOzRDQUFLVCxXQUFVO3NEQUFhSyxRQUFRVCxRQUFROzs7Ozs7c0RBQzdDLDhEQUFDYzs0Q0FBT1YsV0FBVTtzREFBWTs7Ozs7Ozs7Ozs7OzsyQkFOcEJNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFjMUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW5pcy1lcmFzdHVzLXBvcnRmb2xpby8uL3NyYy9jb21wb25lbnRzL0luc2lnaHRzLnRzeD80ZmVlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEluc2lnaHRzKCkge1xuICBjb25zdCBpbnNpZ2h0cyA9IFtcbiAgICB7XG4gICAgICB0aXRsZTogXCJBSSBDaGF0Ym90cyBUaGF0IEFjdHVhbGx5IENvbnZlcnRcIixcbiAgICAgIGRlc2NyaXB0aW9uOlxuICAgICAgICBcIkxlYXJuIGhvdyB0byBidWlsZCBBSSBjaGF0Ym90cyB0aGF0IGRvbid0IGp1c3QgYW5zd2VyIHF1ZXN0aW9ucywgYnV0IGFjdGl2ZWx5IGd1aWRlIHByb3NwZWN0cyB0aHJvdWdoIHlvdXIgc2FsZXMgZnVubmVsLlwiLFxuICAgICAgcmVhZFRpbWU6IFwiNSBtaW4gcmVhZFwiLFxuICAgICAgY2F0ZWdvcnk6IFwiQUkgQXV0b21hdGlvblwiLFxuICAgIH0sXG4gICAge1xuICAgICAgdGl0bGU6IFwiQXV0b21hdGluZyBMZWFkIE51cnR1cmluZyB3aXRoIEFJXCIsXG4gICAgICBkZXNjcmlwdGlvbjpcbiAgICAgICAgXCJEaXNjb3ZlciB0aGUgZXhhY3QgZnJhbWV3b3JrIEkgdXNlIHRvIG51cnR1cmUgbGVhZHMgYXV0b21hdGljYWxseSB3aGlsZSBtYWludGFpbmluZyB0aGF0IHBlcnNvbmFsIHRvdWNoLlwiLFxuICAgICAgcmVhZFRpbWU6IFwiNyBtaW4gcmVhZFwiLFxuICAgICAgY2F0ZWdvcnk6IFwiTGVhZCBHZW5lcmF0aW9uXCIsXG4gICAgfSxcbiAgICB7XG4gICAgICB0aXRsZTogXCJUaGUgUk9JIG9mIEJ1c2luZXNzIFByb2Nlc3MgQXV0b21hdGlvblwiLFxuICAgICAgZGVzY3JpcHRpb246XG4gICAgICAgIFwiUmVhbCBudW1iZXJzIGZyb20gcmVhbCBidXNpbmVzc2VzOiBIb3cgYXV0b21hdGlvbiBkZWxpdmVycyBtZWFzdXJhYmxlIHJldHVybnMgb24gaW52ZXN0bWVudC5cIixcbiAgICAgIHJlYWRUaW1lOiBcIjYgbWluIHJlYWRcIixcbiAgICAgIGNhdGVnb3J5OiBcIkJ1c2luZXNzIFN0cmF0ZWd5XCIsXG4gICAgfSxcbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGlkPVwiaW5zaWdodHNcIiBjbGFzc05hbWU9XCJzZWN0aW9uXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlY3Rpb24taGVhZGVyXCI+XG4gICAgICAgICAgPGgyPkxhdGVzdCBJbnNpZ2h0czwvaDI+XG4gICAgICAgICAgPHA+XG4gICAgICAgICAgICBQcmFjdGljYWwgc3RyYXRlZ2llcyBhbmQgcmVhbC13b3JsZCBjYXNlIHN0dWRpZXMgaW4gQUkgYXV0b21hdGlvblxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnNpZ2h0cy1ncmlkXCI+XG4gICAgICAgICAge2luc2lnaHRzLm1hcCgoaW5zaWdodCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxhcnRpY2xlIGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImluc2lnaHQtY2FyZFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImluc2lnaHQtY2F0ZWdvcnlcIj57aW5zaWdodC5jYXRlZ29yeX08L2Rpdj5cbiAgICAgICAgICAgICAgPGgzPntpbnNpZ2h0LnRpdGxlfTwvaDM+XG4gICAgICAgICAgICAgIDxwPntpbnNpZ2h0LmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnNpZ2h0LW1ldGFcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJyZWFkLXRpbWVcIj57aW5zaWdodC5yZWFkVGltZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJyZWFkLW1vcmVcIj5SZWFkIE1vcmUg4oaSPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9hcnRpY2xlPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvc2VjdGlvbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJJbnNpZ2h0cyIsImluc2lnaHRzIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInJlYWRUaW1lIiwiY2F0ZWdvcnkiLCJzZWN0aW9uIiwiaWQiLCJjbGFzc05hbWUiLCJkaXYiLCJoMiIsInAiLCJtYXAiLCJpbnNpZ2h0IiwiaW5kZXgiLCJhcnRpY2xlIiwiaDMiLCJzcGFuIiwiYnV0dG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Insights.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Story.tsx":
/*!**********************************!*\
  !*** ./src/components/Story.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Story)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Story() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"story\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"story-content-wrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"story-visual\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"story-label\",\n                            children: \"AI Automation Strategist\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                            lineNumber: 7,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"story-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"The Journey to AI-Powered Business Transformation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"From manual chaos to intelligent automation systems\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"My journey began when I witnessed businesses drowning in repetitive tasks and inefficient processes. I discovered that AI automation wasn't just about technology—it was about liberating human potential and creating systems that work smarter, not harder.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Today, I specialize in designing AI automation workflows that transform how businesses operate. From customer service chatbots to automated lead nurturing systems, I help companies build the infrastructure that scales with their ambitions while they focus on what truly matters—growing their business.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Story.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Testimonials() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"testimonials\",\n        className: \"section\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"testimonials-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"What People Are Saying\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Real results from real clients\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"testimonial-mosaic\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fade-edge-left\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fade-edge-right\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mosaic-container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mosaic-column\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card video\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"video-play\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"20px\",\n                                                    left: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"John D.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 25,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"CEO & Founder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 26,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card large gold-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text testimonial-quote\",\n                                                children: \"\\\"Since implementing Denis's automations, our efficiency has skyrocketed. He doesn't just do automation, he transforms businesses.\\\"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Maria Rodriguez\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"CEO Digital Agency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 39,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-stat\",\n                                                children: \"300%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-metric\",\n                                                children: \"process efficiency in 3 months!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"12px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"testimonial-author\",\n                                                    children: \"James Wilson\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mosaic-column\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card medium green-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-avatar\",\n                                                        children: \"AT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-author\",\n                                                                children: \"Alex Turner\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 61,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-role\",\n                                                                children: \"Agency Owner\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 62,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"Best investment in our operations ever. The AI workflows alone are worth 10x what we paid.\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card large\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"Just implemented Denis\\'s AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof \\uD83D\\uDE80\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Emma Davis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"Marketing Director\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card video\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"video-play\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"20px\",\n                                                    left: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Lisa Martinez\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"Startup Founder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mosaic-column\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card small gold-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"1700% growth sounds impossible until you work with Denis. He delivered exactly that for our startup\\'s sales pipeline!\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"12px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"testimonial-author\",\n                                                    children: \"Michael Brown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card large\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-avatar\",\n                                                        children: \"JL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-author\",\n                                                                children: \"Jennifer Lee\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-role\",\n                                                                children: \"Local Business\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"Automated our lead qualification. 300% faster response times. Our sales team is closing deals quicker than ever.\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card medium green-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-stat\",\n                                                children: \"10x\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-metric\",\n                                                children: \"Operational Efficiency. Best investment in our business ever!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Sarah Chen\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"Business Owner\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mosaic-column\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text testimonial-quote\",\n                                                children: '\"an unmatched skill in automation\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"16px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Patricia Williams\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"Creative Director\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card small gold-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-avatar\",\n                                                        children: \"KT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-author\",\n                                                                children: \"Kevin Taylor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-role\",\n                                                                children: \"B2B SaaS Platform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"Automated our data entry and reporting, saving countless hours.\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card large green-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"Just implemented Denis\\'s AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof.\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Robert Anderson\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"VP of Sales\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mosaic-column\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card video\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"video-play\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"20px\",\n                                                    left: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"John D.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"CEO & Founder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card large gold-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text testimonial-quote\",\n                                                children: \"\\\"Since implementing Denis's automations, our efficiency has skyrocketed. He doesn't just do automation, he transforms businesses.\\\"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Maria Rodriguez\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"CEO Digital Agency\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-stat\",\n                                                children: \"300%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-metric\",\n                                                children: \"process efficiency in 3 months!\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"12px\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"testimonial-author\",\n                                                    children: \"James Wilson\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mosaic-column\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card medium green-accent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-avatar\",\n                                                        children: \"AT\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-info\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-author\",\n                                                                children: \"Alex Turner\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"testimonial-role\",\n                                                                children: \"Agency Owner\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"Best investment in our operations ever. The AI workflows alone are worth 10x what we paid.\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card large\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"testimonial-text\",\n                                                children: '\"Just implemented Denis\\'s AI workflow - handling 100+ customer inquiries per day now! Quality is outstanding and team productivity is through the roof \\uD83D\\uDE80\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Emma Davis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"Marketing Director\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"testimonial-card video\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"video-play\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    bottom: \"20px\",\n                                                    left: \"20px\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-author\",\n                                                        children: \"Lisa Martinez\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"testimonial-role\",\n                                                        children: \"Startup Founder\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Testimonials.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/analytics.ts":
/*!******************************!*\
  !*** ./src/lib/analytics.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nasync function trackPageView(page) {\n    try {\n        const analyticsData = {\n            page,\n            user_agent:  false ? 0 : undefined,\n            referrer:  false ? 0 : undefined\n        };\n        // Only track in production or when explicitly enabled\n        if ( false || process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === \"true\") {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.db.analytics.track(analyticsData);\n        }\n    } catch (error) {\n        // Silently fail analytics tracking to not affect user experience\n        console.warn(\"Analytics tracking failed:\", error);\n    }\n}\nasync function trackEvent(eventName, properties) {\n    try {\n        const analyticsData = {\n            page: `event:${eventName}`,\n            user_agent:  false ? 0 : undefined,\n            referrer:  false ? 0 : undefined\n        };\n        if ( false || process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === \"true\") {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.db.analytics.track(analyticsData);\n        }\n    } catch (error) {\n        console.warn(\"Event tracking failed:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/analytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   subscriptions: () => (/* binding */ subscriptions),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://hkbhttezrkjbyoougifv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhrYmh0dGV6cmtqYnlvb3VnaWZ2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTgzOTQsImV4cCI6MjA2NjkzNDM5NH0.M_cWwOHTvSKQdyjvYyyJ1HHhbiImiGqUCfpWvUm_llc\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Database Operations\nconst db = {\n    // Contact operations\n    contacts: {\n        async create (contact) {\n            const { data, error } = await supabase.from(\"contacts\").insert([\n                contact\n            ]).select().single();\n            if (error) throw error;\n            return data;\n        },\n        async getAll () {\n            const { data, error } = await supabase.from(\"contacts\").select(\"*\").order(\"created_at\", {\n                ascending: false\n            });\n            if (error) throw error;\n            return data;\n        },\n        async getById (id) {\n            const { data, error } = await supabase.from(\"contacts\").select(\"*\").eq(\"id\", id).single();\n            if (error) throw error;\n            return data;\n        }\n    },\n    // Analytics operations\n    analytics: {\n        async track (analytics) {\n            const { data, error } = await supabase.from(\"analytics\").insert([\n                analytics\n            ]).select().single();\n            if (error) throw error;\n            return data;\n        },\n        async getPageViews (page) {\n            let query = supabase.from(\"analytics\").select(\"*\").order(\"created_at\", {\n                ascending: false\n            });\n            if (page) {\n                query = query.eq(\"page\", page);\n            }\n            const { data, error } = await query;\n            if (error) throw error;\n            return data;\n        },\n        async getStats () {\n            const { data, error } = await supabase.from(\"analytics\").select(\"page, created_at\").gte(\"created_at\", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());\n            if (error) throw error;\n            // Process stats\n            const pageViews = data.length;\n            const uniquePages = new Set(data.map((item)=>item.page)).size;\n            const dailyViews = data.reduce((acc, item)=>{\n                const date = new Date(item.created_at).toDateString();\n                acc[date] = (acc[date] || 0) + 1;\n                return acc;\n            }, {});\n            return {\n                totalViews: pageViews,\n                uniquePages,\n                dailyViews\n            };\n        }\n    }\n};\n// Real-time subscriptions\nconst subscriptions = {\n    contacts: (callback)=>{\n        return supabase.channel(\"contacts\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"contacts\"\n        }, callback).subscribe();\n    },\n    analytics: (callback)=>{\n        return supabase.channel(\"analytics\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"analytics\"\n        }, callback).subscribe();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c0ec20e31b0f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVuaXMtZXJhc3R1cy1wb3J0Zm9saW8vLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2Q3NTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjMGVjMjBlMzFiMGZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Denis Erastus | AI & Business Automation Expert\",\n    description: \"Transform your business with AI-powered automations. Denis Erastus helps businesses achieve extraordinary growth through strategic AI automation systems.\",\n    keywords: \"AI automation, business automation, process optimization, Denis Erastus, AI expert\",\n    authors: [\n        {\n            name: \"Denis Erastus\"\n        }\n    ],\n    creator: \"Denis Erastus\",\n    publisher: \"Denis Erastus\",\n    openGraph: {\n        title: \"Denis Erastus | AI & Business Automation Expert\",\n        description: \"Transform your business with AI-powered automations. Strategic AI automation systems that work 24/7 to drive results.\",\n        url: \"https://deniserastus.com\",\n        siteName: \"Denis Erastus Portfolio\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Denis Erastus - AI Automation Expert\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Denis Erastus | AI & Business Automation Expert\",\n        description: \"Transform your business with AI-powered automations.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#17b8dd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\poooo\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();