/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*******************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!****************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDREVOSVMlNUNEZXNrdG9wJTVDcG9vb28lNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW5pcy1lcmFzdHVzLXBvcnRmb2xpby8/NjFmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERFTklTXFxcXERlc2t0b3BcXFxccG9vb29cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Hero */ \"(ssr)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_Insights__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Insights */ \"(ssr)/./src/components/Insights.tsx\");\n/* harmony import */ var _components_Story__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Story */ \"(ssr)/./src/components/Story.tsx\");\n/* harmony import */ var _components_CaseStudy__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CaseStudy */ \"(ssr)/./src/components/CaseStudy.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Testimonials */ \"(ssr)/./src/components/Testimonials.tsx\");\n/* harmony import */ var _components_FAQ__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/FAQ */ \"(ssr)/./src/components/FAQ.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/Footer */ \"(ssr)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_ContactModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ContactModal */ \"(ssr)/./src/components/ContactModal.tsx\");\n/* harmony import */ var _components_FloatingContactButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/FloatingContactButton */ \"(ssr)/./src/components/FloatingContactButton.tsx\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/analytics */ \"(ssr)/./src/lib/analytics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Track page view\n        (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_12__.trackPageView)(\"/\");\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloatingContactButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Insights__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Story__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CaseStudy__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FAQ__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AutomationAnimation.tsx":
/*!************************************************!*\
  !*** ./src/components/AutomationAnimation.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AutomationAnimation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction AutomationAnimation() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"automation-animation\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"central-hub\",\n                children: \"AI\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 4,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-1\",\n                children: \"\\uD83D\\uDCCA\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 5,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-2\",\n                children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-3\",\n                children: \"⚡\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-4\",\n                children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-5\",\n                children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"process-node node-6\",\n                children: \"\\uD83D\\uDD04\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-1\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-2\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"automation-line line-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"floating-particles\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"particle\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\AutomationAnimation.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AutomationAnimation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CaseStudy.tsx":
/*!**************************************!*\
  !*** ./src/components/CaseStudy.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CaseStudy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction CaseStudy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"case-study\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"case-study-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"case-study-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"case-study-label\",\n                                children: \"Featured Case Study\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 7,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"How Fokas LLC Increased Revenue by 300% with AI Automation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 8,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"case-study-grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"case-study-details\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"challenge\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"The Challenge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 14,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Fokas LLC was struggling with manual lead qualification and follow-up processes, losing potential customers due to delayed responses and inconsistent communication.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 15,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"solution\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"The Solution\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Implemented an AI-powered lead qualification system with automated follow-up sequences and intelligent chatbot integration.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 20,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"results\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"The Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"metrics\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"metric\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-value\",\n                                                                children: \"300%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 27,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-label\",\n                                                                children: \"Revenue Increase\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 28,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                        lineNumber: 26,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"metric\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-value\",\n                                                                children: \"85%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 31,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-label\",\n                                                                children: \"Time Saved\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 32,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                        lineNumber: 30,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"metric\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-value\",\n                                                                children: \"24/7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 35,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"metric-label\",\n                                                                children: \"Automated Support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                                lineNumber: 36,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                        lineNumber: 34,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"case-study-visual\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"case-study-image-placeholder\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCC8 Success Story\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\CaseStudy.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CaseStudy.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ContactModal.tsx":
/*!*****************************************!*\
  !*** ./src/components/ContactModal.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ContactModal() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Modal functionality\n        const modal = document.getElementById(\"contactModal\");\n        const closeBtn = document.querySelector(\".close-button\");\n        const closeModal = ()=>{\n            if (modal) {\n                modal.classList.remove(\"active\");\n                document.body.style.overflow = \"auto\";\n            }\n        };\n        // Close modal when clicking close button\n        if (closeBtn) {\n            closeBtn.addEventListener(\"click\", closeModal);\n        }\n        // Close modal when clicking outside\n        if (modal) {\n            modal.addEventListener(\"click\", (e)=>{\n                if (e.target === modal) {\n                    closeModal();\n                }\n            });\n        }\n        // Close modal with Escape key\n        const handleEscape = (e)=>{\n            if (e.key === \"Escape\") {\n                closeModal();\n            }\n        };\n        document.addEventListener(\"keydown\", handleEscape);\n        return ()=>{\n            if (closeBtn) {\n                closeBtn.removeEventListener(\"click\", closeModal);\n            }\n            if (modal) {\n                modal.removeEventListener(\"click\", closeModal);\n            }\n            document.removeEventListener(\"keydown\", handleEscape);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"contactModal\",\n        className: \"modal\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"modal-content\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"close-button\",\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"modal-headline\",\n                    children: \"We'd Love to Hear From You!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"modal-subheadline\",\n                    children: \"Choose how you'd like to connect:\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"contact-options\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"mailto:<EMAIL>?subject=AI%20Automation%20Inquiry&body=Hi%20Denis,%0A%0AI'm%20interested%20in%20learning%20more%20about%20your%20AI%20automation%20services.%0A%0ABusiness:%20%0AChallenge:%20%0A%0ALooking%20forward%20to%20hearing%20from%20you!\",\n                            className: \"contact-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    className: \"icon lucide lucide-mail\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            width: \"20\",\n                                            height: \"16\",\n                                            x: \"2\",\n                                            y: \"4\",\n                                            rx: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    children: \"Send an Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Get a detailed response within 24 hours\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"https://calendly.com/your-scheduling-link\",\n                            target: \"_blank\",\n                            className: \"contact-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    className: \"icon lucide lucide-calendar-check\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            width: \"18\",\n                                            height: \"18\",\n                                            x: \"3\",\n                                            y: \"4\",\n                                            rx: \"2\",\n                                            ry: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"16\",\n                                            x2: \"16\",\n                                            y1: \"2\",\n                                            y2: \"6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"8\",\n                                            x2: \"8\",\n                                            y1: \"2\",\n                                            y2: \"6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                            x1: \"3\",\n                                            x2: \"21\",\n                                            y1: \"10\",\n                                            y2: \"10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"m9 16 2 2 4-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    children: \"Book a 30-min Call\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Free strategy session to discuss your needs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"contact-card\",\n                            id: \"openContactForm\",\n                            onClick: ()=>{\n                                // Close modal and show contact form (to be implemented)\n                                const modal = document.getElementById(\"contactModal\");\n                                if (modal) {\n                                    modal.classList.remove(\"active\");\n                                    document.body.style.overflow = \"auto\";\n                                }\n                            // TODO: Open contact form\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"48\",\n                                    height: \"48\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    className: \"icon lucide lucide-form-input\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                            width: \"20\",\n                                            height: \"12\",\n                                            x: \"2\",\n                                            y: \"6\",\n                                            rx: \"2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M12 12h.01\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M17 12h.01\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M7 12h.01\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    children: \"Contact Form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Detailed project inquiry form\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\ContactModal.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ContactModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FAQ.tsx":
/*!********************************!*\
  !*** ./src/components/FAQ.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FAQ)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction FAQ() {\n    const [openIndex, setOpenIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const faqs = [\n        {\n            question: \"What types of business processes can be automated with AI?\",\n            answer: \"I can automate a wide range of processes including customer service (chatbots), lead qualification and nurturing, email marketing sequences, data entry and processing, appointment scheduling, social media management, and sales pipeline management. Essentially, any repetitive task that follows a logical pattern can be automated.\"\n        },\n        {\n            question: \"How do you determine which automations are right for my business?\",\n            answer: \"I start with a comprehensive audit of your current processes to identify bottlenecks and repetitive tasks. Then I analyze your business goals, customer journey, and pain points to prioritize automations that will deliver the highest ROI. We focus on quick wins first, then build more complex systems over time.\"\n        },\n        {\n            question: \"What's the typical timeline for implementing AI automation?\",\n            answer: \"Simple automations like chatbots or email sequences can be implemented in 1-2 weeks. More complex systems involving multiple integrations typically take 4-8 weeks. I work in phases, so you'll see results quickly while we build out the complete system.\"\n        },\n        {\n            question: \"Do you provide ongoing support and maintenance?\",\n            answer: \"Yes, I offer comprehensive support packages that include system monitoring, updates, optimization, and troubleshooting. AI systems need regular fine-tuning to maintain peak performance, and I ensure your automations continue to deliver results as your business grows.\"\n        },\n        {\n            question: \"What kind of results can I expect from AI automation?\",\n            answer: \"Results vary by business, but typical outcomes include 50-80% reduction in manual tasks, 24/7 customer support availability, 200-400% improvement in response times, and 30-60% increase in lead conversion rates. I provide detailed analytics to track ROI and continuously optimize performance.\"\n        },\n        {\n            question: \"How much does AI automation cost?\",\n            answer: \"Investment depends on the complexity and scope of your automation needs. Simple chatbots start around $2,000, while comprehensive automation systems range from $5,000-$20,000. I offer flexible payment plans and the ROI typically pays for the investment within 3-6 months.\"\n        }\n    ];\n    const toggleFAQ = (index)=>{\n        setOpenIndex(openIndex === index ? null : index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"faq\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Everything you need to know about AI automation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"faq-container\",\n                    children: faqs.map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `faq-item ${openIndex === index ? \"active\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"faq-question\",\n                                    onClick: ()=>toggleFAQ(index),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: faq.question\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"faq-icon\",\n                                            children: openIndex === index ? \"−\" : \"+\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-answer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: faq.answer\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FAQ.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FAQ.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FloatingContactButton.tsx":
/*!**************************************************!*\
  !*** ./src/components/FloatingContactButton.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FloatingContactButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction FloatingContactButton() {\n    const openContactModal = ()=>{\n        const modal = document.getElementById(\"contactModal\");\n        if (modal) {\n            modal.classList.add(\"active\");\n            document.body.style.overflow = \"hidden\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"floating-contact-btn\",\n        onClick: openContactModal,\n        id: \"floatingContactBtn\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FloatingContactButton.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FloatingContactButton.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            \"Get In Touch\"\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\FloatingContactButton.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FloatingContactButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Footer() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"footer-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-main\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-brand\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"DENIS ERASTUS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 8,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"AI & Business Automation Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 9,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Transforming businesses through intelligent automation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 10,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 7,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-links\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-column\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 15,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"AI Chatbots\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 17,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 17,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"Process Automation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 18,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 18,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"Lead Generation\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 19,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 19,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#insights\",\n                                                            children: \"Workflow Optimization\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 20,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 20,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 16,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-column\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"Company\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 25,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#story\",\n                                                            children: \"About\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 27,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 27,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#case-study\",\n                                                            children: \"Case Studies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 28,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 28,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#testimonials\",\n                                                            children: \"Testimonials\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 29,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 29,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#faq\",\n                                                            children: \"FAQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 30,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 30,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"footer-column\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"mailto:<EMAIL>\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 37,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"https://calendly.com/your-scheduling-link\",\n                                                            children: \"Book a Call\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 38,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"#hero\",\n                                                            children: \"Get In Touch\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 39,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 39,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer-bottom\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-social\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        \"aria-label\": \"LinkedIn\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        \"aria-label\": \"Twitter\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"20\",\n                                            height: \"20\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"footer-copyright\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"\\xa9 2025 Denis Erastus. All rights reserved.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header() {\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    const openContactModal = ()=>{\n        const modal = document.getElementById(\"contactModal\");\n        if (modal) {\n            modal.classList.add(\"active\");\n            document.body.style.overflow = \"hidden\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"#hero\",\n                    className: \"logo\",\n                    children: \"DENIS ERASTUS\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: `nav-links ${mobileMenuOpen ? \"active\" : \"\"}`,\n                    id: \"navMenu\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#hero\",\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#insights\",\n                                children: \"Services\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#case-study\",\n                                children: \"Portfolio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#testimonials\",\n                                children: \"Reviews\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#faq\",\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"nav-cta\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"btn btn-primary\",\n                        onClick: openContactModal,\n                        id: \"getInTouchNavBtn\",\n                        children: \"Get In Touch\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"mobile-menu\",\n                    id: \"mobileMenu\",\n                    onClick: toggleMobileMenu,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Header.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ0o7QUFFYixTQUFTRTtJQUN0QixNQUFNLENBQUNDLGdCQUFnQkMsa0JBQWtCLEdBQUdKLCtDQUFRQSxDQUFDO0lBRXJELE1BQU1LLG1CQUFtQjtRQUN2QkQsa0JBQWtCLENBQUNEO0lBQ3JCO0lBRUEsTUFBTUcsbUJBQW1CO1FBQ3ZCLE1BQU1DLFFBQVFDLFNBQVNDLGNBQWMsQ0FBQztRQUN0QyxJQUFJRixPQUFPO1lBQ1RBLE1BQU1HLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO1lBQ3BCSCxTQUFTSSxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsUUFBUSxHQUFHO1FBQ2pDO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0M7a0JBQ0MsNEVBQUNDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDaEIsa0RBQUlBO29CQUFDaUIsTUFBSztvQkFBUUQsV0FBVTs4QkFBTzs7Ozs7OzhCQUlwQyw4REFBQ0U7b0JBQUdGLFdBQVcsQ0FBQyxVQUFVLEVBQUVkLGlCQUFpQixXQUFXLEdBQUcsQ0FBQztvQkFBRWlCLElBQUc7O3NDQUMvRCw4REFBQ0M7c0NBQUcsNEVBQUNwQixrREFBSUE7Z0NBQUNpQixNQUFLOzBDQUFROzs7Ozs7Ozs7OztzQ0FDdkIsOERBQUNHO3NDQUFHLDRFQUFDcEIsa0RBQUlBO2dDQUFDaUIsTUFBSzswQ0FBWTs7Ozs7Ozs7Ozs7c0NBQzNCLDhEQUFDRztzQ0FBRyw0RUFBQ3BCLGtEQUFJQTtnQ0FBQ2lCLE1BQUs7MENBQWM7Ozs7Ozs7Ozs7O3NDQUM3Qiw4REFBQ0c7c0NBQUcsNEVBQUNwQixrREFBSUE7Z0NBQUNpQixNQUFLOzBDQUFnQjs7Ozs7Ozs7Ozs7c0NBQy9CLDhEQUFDRztzQ0FBRyw0RUFBQ3BCLGtEQUFJQTtnQ0FBQ2lCLE1BQUs7MENBQU87Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUd4Qiw4REFBQ0k7b0JBQUlMLFdBQVU7OEJBQ2IsNEVBQUNNO3dCQUNDTixXQUFVO3dCQUNWTyxTQUFTbEI7d0JBQ1RjLElBQUc7a0NBQ0o7Ozs7Ozs7Ozs7OzhCQUtILDhEQUFDRztvQkFDQ04sV0FBVTtvQkFDVkcsSUFBRztvQkFDSEksU0FBU25COztzQ0FFVCw4REFBQ29COzs7OztzQ0FDRCw4REFBQ0E7Ozs7O3NDQUNELDhEQUFDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVuaXMtZXJhc3R1cy1wb3J0Zm9saW8vLi9zcmMvY29tcG9uZW50cy9IZWFkZXIudHN4P2E2OTYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhlYWRlcigpIHtcbiAgY29uc3QgW21vYmlsZU1lbnVPcGVuLCBzZXRNb2JpbGVNZW51T3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCB0b2dnbGVNb2JpbGVNZW51ID0gKCkgPT4ge1xuICAgIHNldE1vYmlsZU1lbnVPcGVuKCFtb2JpbGVNZW51T3BlbilcbiAgfVxuXG4gIGNvbnN0IG9wZW5Db250YWN0TW9kYWwgPSAoKSA9PiB7XG4gICAgY29uc3QgbW9kYWwgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnY29udGFjdE1vZGFsJylcbiAgICBpZiAobW9kYWwpIHtcbiAgICAgIG1vZGFsLmNsYXNzTGlzdC5hZGQoJ2FjdGl2ZScpXG4gICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJ2hpZGRlbidcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxoZWFkZXI+XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImNvbnRhaW5lclwiPlxuICAgICAgICA8TGluayBocmVmPVwiI2hlcm9cIiBjbGFzc05hbWU9XCJsb2dvXCI+XG4gICAgICAgICAgREVOSVMgRVJBU1RVU1xuICAgICAgICA8L0xpbms+XG4gICAgICAgIFxuICAgICAgICA8dWwgY2xhc3NOYW1lPXtgbmF2LWxpbmtzICR7bW9iaWxlTWVudU9wZW4gPyAnYWN0aXZlJyA6ICcnfWB9IGlkPVwibmF2TWVudVwiPlxuICAgICAgICAgIDxsaT48TGluayBocmVmPVwiI2hlcm9cIj5Ib21lPC9MaW5rPjwvbGk+XG4gICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIjaW5zaWdodHNcIj5TZXJ2aWNlczwvTGluaz48L2xpPlxuICAgICAgICAgIDxsaT48TGluayBocmVmPVwiI2Nhc2Utc3R1ZHlcIj5Qb3J0Zm9saW88L0xpbms+PC9saT5cbiAgICAgICAgICA8bGk+PExpbmsgaHJlZj1cIiN0ZXN0aW1vbmlhbHNcIj5SZXZpZXdzPC9MaW5rPjwvbGk+XG4gICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIjZmFxXCI+RkFRPC9MaW5rPjwvbGk+XG4gICAgICAgIDwvdWw+XG4gICAgICAgIFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm5hdi1jdGFcIj5cbiAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYnRuIGJ0bi1wcmltYXJ5XCIgXG4gICAgICAgICAgICBvbkNsaWNrPXtvcGVuQ29udGFjdE1vZGFsfVxuICAgICAgICAgICAgaWQ9XCJnZXRJblRvdWNoTmF2QnRuXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBHZXQgSW4gVG91Y2hcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIFxuICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgIGNsYXNzTmFtZT1cIm1vYmlsZS1tZW51XCIgXG4gICAgICAgICAgaWQ9XCJtb2JpbGVNZW51XCJcbiAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVNb2JpbGVNZW51fVxuICAgICAgICA+XG4gICAgICAgICAgPHNwYW4+PC9zcGFuPlxuICAgICAgICAgIDxzcGFuPjwvc3Bhbj5cbiAgICAgICAgICA8c3Bhbj48L3NwYW4+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9uYXY+XG4gICAgPC9oZWFkZXI+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkxpbmsiLCJIZWFkZXIiLCJtb2JpbGVNZW51T3BlbiIsInNldE1vYmlsZU1lbnVPcGVuIiwidG9nZ2xlTW9iaWxlTWVudSIsIm9wZW5Db250YWN0TW9kYWwiLCJtb2RhbCIsImRvY3VtZW50IiwiZ2V0RWxlbWVudEJ5SWQiLCJjbGFzc0xpc3QiLCJhZGQiLCJib2R5Iiwic3R5bGUiLCJvdmVyZmxvdyIsImhlYWRlciIsIm5hdiIsImNsYXNzTmFtZSIsImhyZWYiLCJ1bCIsImlkIiwibGkiLCJkaXYiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AutomationAnimation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AutomationAnimation */ \"(ssr)/./src/components/AutomationAnimation.tsx\");\n\n\n\nfunction Hero() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"hero\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hero-content\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-text\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                children: [\n                                    \"Transform Your Business Into A\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"highlight\",\n                                        children: \"Revenue Machine\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 12,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" With\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"highlight\",\n                                        children: \"AI-Powered Automations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"subheadline\",\n                                children: \"I help businesses achieve extraordinary growth by implementing strategic AI automation systems that work 24/7 to drive results.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-stats\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"AI Automation Specialist\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Process Optimization Expert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"4+ Years Experience\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hero-cta\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#case-study\",\n                                        className: \"btn btn-primary\",\n                                        children: \"View Case Studies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"https://calendly.com/your-scheduling-link\",\n                                        target: \"_blank\",\n                                        className: \"btn btn-secondary\",\n                                        children: \"Book a Call\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hero-image\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AutomationAnimation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Hero.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Insights.tsx":
/*!*************************************!*\
  !*** ./src/components/Insights.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Insights)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Insights() {\n    const insights = [\n        {\n            title: \"AI Chatbots That Actually Convert\",\n            description: \"Learn how to build AI chatbots that don't just answer questions, but actively guide prospects through your sales funnel.\",\n            readTime: \"5 min read\",\n            category: \"AI Automation\"\n        },\n        {\n            title: \"Automating Lead Nurturing with AI\",\n            description: \"Discover the exact framework I use to nurture leads automatically while maintaining that personal touch.\",\n            readTime: \"7 min read\",\n            category: \"Lead Generation\"\n        },\n        {\n            title: \"The ROI of Business Process Automation\",\n            description: \"Real numbers from real businesses: How automation delivers measurable returns on investment.\",\n            readTime: \"6 min read\",\n            category: \"Business Strategy\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"insights\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Latest Insights\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Practical strategies and real-world case studies in AI automation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"insights-grid\",\n                    children: insights.map((insight, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                            className: \"insight-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"insight-category\",\n                                    children: insight.category\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: insight.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: insight.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"insight-meta\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"read-time\",\n                                            children: insight.readTime\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"read-more\",\n                                            children: \"Read More →\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Insights.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Insights.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Story.tsx":
/*!**********************************!*\
  !*** ./src/components/Story.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Story)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Story() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"story\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"story-content-wrapper\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"story-visual\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"story-label\",\n                            children: \"AI Automation Strategist\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                            lineNumber: 7,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                        lineNumber: 6,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"story-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"The Journey to AI-Powered Business Transformation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"From manual chaos to intelligent automation systems\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"My journey began when I witnessed businesses drowning in repetitive tasks and inefficient processes. I discovered that AI automation wasn't just about technology—it was about liberating human potential and creating systems that work smarter, not harder.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Today, I specialize in designing AI automation workflows that transform how businesses operate. From customer service chatbots to automated lead nurturing systems, I help companies build the infrastructure that scales with their ambitions while they focus on what truly matters—growing their business.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Story.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Story.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Testimonials() {\n    const testimonials = [\n        {\n            name: \"Sarah Johnson\",\n            company: \"TechStart Inc.\",\n            text: \"Denis transformed our customer service with AI automation. Response times went from hours to seconds!\",\n            rating: 5\n        },\n        {\n            name: \"Michael Chen\",\n            company: \"Growth Solutions\",\n            text: \"The lead nurturing system Denis built increased our conversion rate by 250%. Incredible results!\",\n            rating: 5\n        },\n        {\n            name: \"Emily Rodriguez\",\n            company: \"Digital Marketing Pro\",\n            text: \"Working with Denis was a game-changer. Our processes are now fully automated and efficient.\",\n            rating: 5\n        },\n        {\n            name: \"David Thompson\",\n            company: \"E-commerce Plus\",\n            text: \"The AI chatbot Denis created handles 80% of our customer inquiries automatically.\",\n            rating: 5\n        },\n        {\n            name: \"Lisa Wang\",\n            company: \"Consulting Firm\",\n            text: \"Denis's automation solutions saved us 20 hours per week. ROI was immediate.\",\n            rating: 5\n        },\n        {\n            name: \"James Miller\",\n            company: \"SaaS Startup\",\n            text: \"The automated workflows Denis implemented scaled our business without adding staff.\",\n            rating: 5\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"testimonials\",\n        className: \"section\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"What Clients Say\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Real results from real businesses\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"testimonials-mosaic\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"testimonial-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"testimonial-rating\",\n                                    children: [\n                                        ...Array(testimonial.rating)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"star\",\n                                            children: \"⭐\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"testimonial-text\",\n                                    children: [\n                                        '\"',\n                                        testimonial.text,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"testimonial-author\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: testimonial.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: testimonial.company\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Testimonials.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/analytics.ts":
/*!******************************!*\
  !*** ./src/lib/analytics.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nasync function trackPageView(page) {\n    try {\n        const analyticsData = {\n            page,\n            user_agent:  false ? 0 : undefined,\n            referrer:  false ? 0 : undefined\n        };\n        // Only track in production or when explicitly enabled\n        if ( false || process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === \"true\") {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.db.analytics.track(analyticsData);\n        }\n    } catch (error) {\n        // Silently fail analytics tracking to not affect user experience\n        console.warn(\"Analytics tracking failed:\", error);\n    }\n}\nasync function trackEvent(eventName, properties) {\n    try {\n        const analyticsData = {\n            page: `event:${eventName}`,\n            user_agent:  false ? 0 : undefined,\n            referrer:  false ? 0 : undefined\n        };\n        if ( false || process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === \"true\") {\n            await _supabase__WEBPACK_IMPORTED_MODULE_0__.db.analytics.track(analyticsData);\n        }\n    } catch (error) {\n        console.warn(\"Event tracking failed:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FuYWx5dGljcy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0I7QUFTeEIsZUFBZUMsY0FBY0MsSUFBWTtJQUM5QyxJQUFJO1FBQ0YsTUFBTUMsZ0JBQStCO1lBQ25DRDtZQUNBRSxZQUFZLE1BQWtCLEdBQWNDLENBQW1CLEdBQUdFO1lBQ2xFQyxVQUFVLE1BQWtCLEdBQWNDLENBQWlCLEdBQUdGO1FBQ2hFO1FBRUEsc0RBQXNEO1FBQ3RELElBQUlHLE1BQXlCLElBQWdCQSxRQUFRQyxHQUFHLENBQUNDLDRCQUE0QixLQUFLLFFBQVE7WUFDaEcsTUFBTVoseUNBQUVBLENBQUNhLFNBQVMsQ0FBQ0MsS0FBSyxDQUFDWDtRQUMzQjtJQUNGLEVBQUUsT0FBT1ksT0FBTztRQUNkLGlFQUFpRTtRQUNqRUMsUUFBUUMsSUFBSSxDQUFDLDhCQUE4QkY7SUFDN0M7QUFDRjtBQUVPLGVBQWVHLFdBQVdDLFNBQWlCLEVBQUVDLFVBQWdDO0lBQ2xGLElBQUk7UUFDRixNQUFNakIsZ0JBQStCO1lBQ25DRCxNQUFNLENBQUMsTUFBTSxFQUFFaUIsVUFBVSxDQUFDO1lBQzFCZixZQUFZLE1BQWtCLEdBQWNDLENBQW1CLEdBQUdFO1lBQ2xFQyxVQUFVLE1BQWtCLEdBQWNDLENBQWlCLEdBQUdGO1FBQ2hFO1FBRUEsSUFBSUcsTUFBeUIsSUFBZ0JBLFFBQVFDLEdBQUcsQ0FBQ0MsNEJBQTRCLEtBQUssUUFBUTtZQUNoRyxNQUFNWix5Q0FBRUEsQ0FBQ2EsU0FBUyxDQUFDQyxLQUFLLENBQUNYO1FBQzNCO0lBQ0YsRUFBRSxPQUFPWSxPQUFPO1FBQ2RDLFFBQVFDLElBQUksQ0FBQywwQkFBMEJGO0lBQ3pDO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZW5pcy1lcmFzdHVzLXBvcnRmb2xpby8uL3NyYy9saWIvYW5hbHl0aWNzLnRzPzhhYjgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGIgfSBmcm9tICcuL3N1cGFiYXNlJ1xuXG5leHBvcnQgaW50ZXJmYWNlIEFuYWx5dGljc0RhdGEge1xuICBwYWdlOiBzdHJpbmdcbiAgdXNlcl9hZ2VudD86IHN0cmluZ1xuICByZWZlcnJlcj86IHN0cmluZ1xuICBpcF9hZGRyZXNzPzogc3RyaW5nXG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB0cmFja1BhZ2VWaWV3KHBhZ2U6IHN0cmluZykge1xuICB0cnkge1xuICAgIGNvbnN0IGFuYWx5dGljc0RhdGE6IEFuYWx5dGljc0RhdGEgPSB7XG4gICAgICBwYWdlLFxuICAgICAgdXNlcl9hZ2VudDogdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBuYXZpZ2F0b3IudXNlckFnZW50IDogdW5kZWZpbmVkLFxuICAgICAgcmVmZXJyZXI6IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gZG9jdW1lbnQucmVmZXJyZXIgOiB1bmRlZmluZWQsXG4gICAgfVxuXG4gICAgLy8gT25seSB0cmFjayBpbiBwcm9kdWN0aW9uIG9yIHdoZW4gZXhwbGljaXRseSBlbmFibGVkXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicgfHwgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRU5BQkxFX0FOQUxZVElDUyA9PT0gJ3RydWUnKSB7XG4gICAgICBhd2FpdCBkYi5hbmFseXRpY3MudHJhY2soYW5hbHl0aWNzRGF0YSlcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgLy8gU2lsZW50bHkgZmFpbCBhbmFseXRpY3MgdHJhY2tpbmcgdG8gbm90IGFmZmVjdCB1c2VyIGV4cGVyaWVuY2VcbiAgICBjb25zb2xlLndhcm4oJ0FuYWx5dGljcyB0cmFja2luZyBmYWlsZWQ6JywgZXJyb3IpXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHRyYWNrRXZlbnQoZXZlbnROYW1lOiBzdHJpbmcsIHByb3BlcnRpZXM/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYW5hbHl0aWNzRGF0YTogQW5hbHl0aWNzRGF0YSA9IHtcbiAgICAgIHBhZ2U6IGBldmVudDoke2V2ZW50TmFtZX1gLFxuICAgICAgdXNlcl9hZ2VudDogdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBuYXZpZ2F0b3IudXNlckFnZW50IDogdW5kZWZpbmVkLFxuICAgICAgcmVmZXJyZXI6IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gZG9jdW1lbnQucmVmZXJyZXIgOiB1bmRlZmluZWQsXG4gICAgfVxuXG4gICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicgfHwgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRU5BQkxFX0FOQUxZVElDUyA9PT0gJ3RydWUnKSB7XG4gICAgICBhd2FpdCBkYi5hbmFseXRpY3MudHJhY2soYW5hbHl0aWNzRGF0YSlcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS53YXJuKCdFdmVudCB0cmFja2luZyBmYWlsZWQ6JywgZXJyb3IpXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJkYiIsInRyYWNrUGFnZVZpZXciLCJwYWdlIiwiYW5hbHl0aWNzRGF0YSIsInVzZXJfYWdlbnQiLCJuYXZpZ2F0b3IiLCJ1c2VyQWdlbnQiLCJ1bmRlZmluZWQiLCJyZWZlcnJlciIsImRvY3VtZW50IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0VOQUJMRV9BTkFMWVRJQ1MiLCJhbmFseXRpY3MiLCJ0cmFjayIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJ0cmFja0V2ZW50IiwiZXZlbnROYW1lIiwicHJvcGVydGllcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/analytics.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   subscriptions: () => (/* binding */ subscriptions),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://hkbhttezrkjbyoougifv.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhrYmh0dGV6cmtqYnlvb3VnaWZ2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzNTgzOTQsImV4cCI6MjA2NjkzNDM5NH0.M_cWwOHTvSKQdyjvYyyJ1HHhbiImiGqUCfpWvUm_llc\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Database Operations\nconst db = {\n    // Contact operations\n    contacts: {\n        async create (contact) {\n            const { data, error } = await supabase.from(\"contacts\").insert([\n                contact\n            ]).select().single();\n            if (error) throw error;\n            return data;\n        },\n        async getAll () {\n            const { data, error } = await supabase.from(\"contacts\").select(\"*\").order(\"created_at\", {\n                ascending: false\n            });\n            if (error) throw error;\n            return data;\n        },\n        async getById (id) {\n            const { data, error } = await supabase.from(\"contacts\").select(\"*\").eq(\"id\", id).single();\n            if (error) throw error;\n            return data;\n        }\n    },\n    // Analytics operations\n    analytics: {\n        async track (analytics) {\n            const { data, error } = await supabase.from(\"analytics\").insert([\n                analytics\n            ]).select().single();\n            if (error) throw error;\n            return data;\n        },\n        async getPageViews (page) {\n            let query = supabase.from(\"analytics\").select(\"*\").order(\"created_at\", {\n                ascending: false\n            });\n            if (page) {\n                query = query.eq(\"page\", page);\n            }\n            const { data, error } = await query;\n            if (error) throw error;\n            return data;\n        },\n        async getStats () {\n            const { data, error } = await supabase.from(\"analytics\").select(\"page, created_at\").gte(\"created_at\", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());\n            if (error) throw error;\n            // Process stats\n            const pageViews = data.length;\n            const uniquePages = new Set(data.map((item)=>item.page)).size;\n            const dailyViews = data.reduce((acc, item)=>{\n                const date = new Date(item.created_at).toDateString();\n                acc[date] = (acc[date] || 0) + 1;\n                return acc;\n            }, {});\n            return {\n                totalViews: pageViews,\n                uniquePages,\n                dailyViews\n            };\n        }\n    }\n};\n// Real-time subscriptions\nconst subscriptions = {\n    contacts: (callback)=>{\n        return supabase.channel(\"contacts\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"contacts\"\n        }, callback).subscribe();\n    },\n    analytics: (callback)=>{\n        return supabase.channel(\"analytics\").on(\"postgres_changes\", {\n            event: \"*\",\n            schema: \"public\",\n            table: \"analytics\"\n        }, callback).subscribe();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"08e02071d68a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVuaXMtZXJhc3R1cy1wb3J0Zm9saW8vLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2Q3NTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOGUwMjA3MWQ2OGFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Denis Erastus | AI & Business Automation Expert\",\n    description: \"Transform your business with AI-powered automations. Denis Erastus helps businesses achieve extraordinary growth through strategic AI automation systems.\",\n    keywords: \"AI automation, business automation, process optimization, Denis Erastus, AI expert\",\n    authors: [\n        {\n            name: \"Denis Erastus\"\n        }\n    ],\n    creator: \"Denis Erastus\",\n    publisher: \"Denis Erastus\",\n    openGraph: {\n        title: \"Denis Erastus | AI & Business Automation Expert\",\n        description: \"Transform your business with AI-powered automations. Strategic AI automation systems that work 24/7 to drive results.\",\n        url: \"https://deniserastus.com\",\n        siteName: \"Denis Erastus Portfolio\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Denis Erastus - AI Automation Expert\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Denis Erastus | AI & Business Automation Expert\",\n        description: \"Transform your business with AI-powered automations.\",\n        images: [\n            \"/og-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#17b8dd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\poooo\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\poooo\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/isows","vendor-chunks/tr46"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDENIS%5CDesktop%5Cpoooo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();