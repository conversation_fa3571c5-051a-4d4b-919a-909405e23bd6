{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "64ef1bad6532a32ceb6fd1d308591fe5", "previewModeSigningKey": "b0e1079e36354e841c0ca514d3540253235b1ace9b66772ae9bbd5c2695a60e8", "previewModeEncryptionKey": "322d3a06f8078fb1f47919b17da92470a9ea912416c2af53f3a158313ece6a52"}}