{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "c4115056f69dbd70a43cd1b62d177c55", "previewModeSigningKey": "6060e79d6857ff32284b653564d763070a067377c101cde639a87ada9d90849c", "previewModeEncryptionKey": "208d1c826481a589683d32be5fab1acfb80a6b75ad829c16e89798755e50c6ca"}}