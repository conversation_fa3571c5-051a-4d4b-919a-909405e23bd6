{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "3c0aee1b0e07321770e7be50a32f6ff3", "previewModeSigningKey": "960c0924925999e3fd40632123b98a14a974796430b19400b18bf1478ce4cff4", "previewModeEncryptionKey": "2b60e16d1f2ba661cb0e412fd153d10828bd6efb98ed11589581ba613f6c0a1b"}}