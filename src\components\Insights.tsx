export default function Insights() {
  const insights = [
    {
      title: "AI Chatbots That Actually Convert",
      description:
        "Learn how to build AI chatbots that don't just answer questions, but actively guide prospects through your sales funnel.",
      readTime: "5 min read",
      category: "AI Automation",
    },
    {
      title: "Automating Lead Nurturing with AI",
      description:
        "Discover the exact framework I use to nurture leads automatically while maintaining that personal touch.",
      readTime: "7 min read",
      category: "Lead Generation",
    },
    {
      title: "The ROI of Business Process Automation",
      description:
        "Real numbers from real businesses: How automation delivers measurable returns on investment.",
      readTime: "6 min read",
      category: "Business Strategy",
    },
  ];

  return (
    <section id="insights" className="section">
      <div className="container">
        <div className="section-header">
          <h2>Latest Insights</h2>
          <p>
            Practical strategies and real-world case studies in AI automation
          </p>
        </div>

        <div className="insights-grid">
          {insights.map((insight, index) => (
            <article key={index} className="insight-card">
              <div className="insight-category">{insight.category}</div>
              <h3>{insight.title}</h3>
              <p>{insight.description}</p>
              <div className="insight-meta">
                <span className="read-time">{insight.readTime}</span>
                <button className="read-more">Read More →</button>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
}
