/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ["localhost"],
    formats: ["image/webp", "image/avif"],
  },
  // Enable strict mode for better development experience
  reactStrictMode: true,

  // Optimize for production
  swcMinify: true,

  // Configure headers for security and performance
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "origin-when-cross-origin",
          },
        ],
      },
    ];
  },

  // Configure redirects if needed
  async redirects() {
    return [
      // Add redirects here if needed
    ];
  },
};

module.exports = nextConfig;
