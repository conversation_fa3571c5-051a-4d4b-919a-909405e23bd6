# Denis Erastus Portfolio

> AI Automation Expert Portfolio - Modern Next.js application with Supabase integration

[![CI/CD Pipeline](https://github.com/ProDevDenis/MyPortfolio/actions/workflows/ci.yml/badge.svg)](https://github.com/ProDevDenis/MyPortfolio/actions/workflows/ci.yml)
[![Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black)](https://vercel.com)
[![Supabase](https://img.shields.io/badge/Database-Supabase-green)](https://supabase.com)

## 🚀 Live Demo

- **Production**: [deniserastus.com](https://deniserastus.com)
- **Staging**: [myportfolio-staging.vercel.app](https://myportfolio-staging.vercel.app)

## 🎯 Project Overview

Professional portfolio website for Denis Erastus, showcasing AI automation expertise and services. Built with modern web technologies and best practices.

### ✨ Features

- **Responsive Design** - Optimized for all devices
- **AI Automation Focus** - Showcases expertise and case studies
- **Contact Management** - Integrated contact forms with database storage
- **Analytics Tracking** - Custom analytics implementation
- **SEO Optimized** - Server-side rendering for better search visibility
- **Performance Focused** - Sub-2 second loading times

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Custom CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel
- **CI/CD**: GitHub Actions

## 📋 Quick Start

### Prerequisites

- Node.js 18+
- Git
- GitHub account
- Vercel account
- Supabase account

### Installation

```bash
# Clone the repository
git clone https://github.com/ProDevDenis/MyPortfolio.git
cd MyPortfolio

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
MyPortfolio/
├── docs/                     # Documentation
├── src/
│   ├── app/                  # Next.js App Router
│   ├── components/           # React Components
│   ├── lib/                  # Utilities & Configuration
│   └── styles/               # CSS Styles
├── public/                   # Static Assets
├── .github/workflows/        # GitHub Actions
├── tests/                    # Test Files
└── PROJECT_PLAN.md          # Project roadmap
```

## 🔄 Development Workflow

### Branch Strategy

- `main` - Production-ready code
- `develop` - Integration branch
- `feature/*` - Feature development
- `hotfix/*` - Critical fixes

### Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
npm run type-check   # TypeScript checking
npm run format       # Format with Prettier

# Testing
npm run test         # Run tests
npm run test:watch   # Watch mode
npm run test:coverage # Coverage report
```

## 🚀 Deployment

### Automatic Deployment

- **Staging**: Deploys automatically on push to `develop`
- **Production**: Deploys automatically on push to `main`

### Manual Deployment

```bash
# Deploy to staging
vercel --target staging

# Deploy to production
vercel --prod
```

## 📊 Environment Variables

Required environment variables (see `.env.example`):

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 📚 Documentation

Comprehensive documentation is available in the [`docs/`](./docs/) directory:

- [Setup Guide](./docs/SETUP.md)
- [Development Guide](./docs/DEVELOPMENT.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)
- [API Documentation](./docs/API.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact

**Denis Erastus**

- Website: [deniserastus.com](https://deniserastus.com)
- Email: <EMAIL>
- GitHub: [@ProDevDenis](https://github.com/ProDevDenis)

---

_Built with ❤️ by Denis Erastus_
