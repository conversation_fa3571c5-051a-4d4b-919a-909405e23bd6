"use client";

import { useEffect } from "react";
import Header from "@/components/Header";
import <PERSON> from "@/components/Hero";
import Insights from "@/components/Insights";
import Story from "@/components/Story";
import CaseStudy from "@/components/CaseStudy";
import Testimonials from "@/components/Testimonials";
import FAQ from "@/components/FAQ";
import Footer from "@/components/Footer";
import ContactModal from "@/components/ContactModal";
import FloatingContactButton from "@/components/FloatingContactButton";
import { trackPageView } from "@/lib/analytics";

export default function Home() {
  useEffect(() => {
    // Track page view
    trackPageView("/");
  }, []);

  return (
    <>
      <Header />
      <FloatingContactButton />
      <ContactModal />

      <main>
        <Hero />
        <Insights />
        <Story />
        <CaseStudy />
        <Testimonials />
        <FAQ />
      </main>

      <Footer />
    </>
  );
}
