import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "<PERSON> | AI & Business Automation Expert",
  description:
    "Transform your business with AI-powered automations. <PERSON> helps businesses achieve extraordinary growth through strategic AI automation systems.",
  keywords:
    "AI automation, business automation, process optimization, <PERSON>, AI expert",
  authors: [{ name: "<PERSON>" }],
  creator: "<PERSON>",
  publisher: "<PERSON>",
  openGraph: {
    title: "<PERSON> | AI & Business Automation Expert",
    description:
      "Transform your business with AI-powered automations. Strategic AI automation systems that work 24/7 to drive results.",
    url: "https://deniserastus.com",
    siteName: "Denis Erastus Portfolio",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Denis Erastus - AI Automation Expert",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "<PERSON> | AI & Business Automation Expert",
    description: "Transform your business with AI-powered automations.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#17b8dd" />
      </head>
      <body>{children}</body>
    </html>
  );
}
